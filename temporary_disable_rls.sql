-- Temporary disable <PERSON><PERSON> for testing couple connection
-- ONLY FOR TESTING - Re-enable after testing!

-- Disable <PERSON><PERSON> temporarily
ALTER TABLE couples DISABLE ROW LEVEL SECURITY;
ALTER TABLE individual_results DISABLE ROW LEVEL SECURITY;
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Check RLS status
SELECT 
  schemaname, 
  tablename, 
  rowsecurity 
FROM pg_tables 
WHERE tablename IN ('couples', 'individual_results', 'profiles', 'couple_invitation_codes');

-- TO RE-ENABLE LATER (run this after testing):
/*
ALTER TABLE couples ENABLE ROW LEVEL SECURITY;
ALTER TABLE individual_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
*/
