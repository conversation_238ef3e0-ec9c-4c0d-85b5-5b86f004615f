-- Test script to verify couple connection functionality
-- Run this in Supabase SQL Editor after testing

-- 1. Check all users in profiles table
SELECT 
  id,
  full_name,
  email,
  created_at
FROM profiles
ORDER BY created_at DESC;

-- 2. Check all invitation codes
SELECT 
  id,
  code,
  creator_user_id,
  used_by_user_id,
  expires_at,
  used_at,
  is_active,
  created_at
FROM couple_invitation_codes
ORDER BY created_at DESC;

-- 3. Check all couples
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at,
  (SELECT full_name FROM profiles WHERE id = couples.user_id_1) as user_1_name,
  (SELECT full_name FROM profiles WHERE id = couples.user_id_2) as user_2_name
FROM couples
ORDER BY created_at DESC;

-- 4. Check couple connection details with names
SELECT 
  c.couple_id,
  c.created_at as couple_created,
  p1.full_name as partner_1_name,
  p1.email as partner_1_email,
  p2.full_name as partner_2_name,
  p2.email as partner_2_email,
  cic.code as invitation_code_used,
  cic.used_at
FROM couples c
LEFT JOIN profiles p1 ON c.user_id_1 = p1.id
LEFT JOIN profiles p2 ON c.user_id_2 = p2.id
LEFT JOIN couple_invitation_codes cic ON (cic.creator_user_id = c.user_id_1 AND cic.used_by_user_id = c.user_id_2)
ORDER BY c.created_at DESC;
