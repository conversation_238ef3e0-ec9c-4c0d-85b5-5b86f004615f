-- Add progress tracking to existing individual_results table
-- This migration adds the current_progress column if it doesn't exist

-- Add current_progress column to track assessment progress per domain
ALTER TABLE individual_results 
ADD COLUMN IF NOT EXISTS current_progress JSONB DEFAULT '{}';

-- Create index for better performance on progress queries
CREATE INDEX IF NOT EXISTS idx_individual_results_user_progress 
ON individual_results(user_id, (current_progress));

-- Update existing records to have empty progress object
UPDATE individual_results 
SET current_progress = '{}' 
WHERE current_progress IS NULL;
