-- Add policy to allow public read access to counselor profiles for selection
DROP POLICY IF EXISTS "Public can view counselor profiles" ON counselor_profiles;
CREATE POLICY "Public can view counselor profiles"
  ON counselor_profiles FOR SELECT
  USING (true);

-- Add policy to allow couples to view their assigned counselor
DROP POLICY IF EXISTS "Couples can view their assignments" ON counselor_couple_assignments;
CREATE POLICY "Couples can view their assignments"
  ON counselor_couple_assignments FOR SELECT
  USING (
    couple_id IN (
      SELECT couple_id FROM couples 
      WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
    )
  );

-- Add policy to allow couples to create assignments (select counselor)
DROP POLICY IF EXISTS "Couples can create assignments" ON counselor_couple_assignments;
CREATE POLICY "Couples can create assignments"
  ON counselor_couple_assignments FOR INSERT
  WITH CHECK (
    couple_id IN (
      SELECT couple_id FROM couples 
      WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
    )
  );

-- Add policy to allow couples to update their assignments (change counselor)
DROP POLICY IF EXISTS "Couples can update assignments" ON counselor_couple_assignments;
CREATE POLICY "Couples can update assignments"
  ON counselor_couple_assignments FOR UPDATE
  USING (
    couple_id IN (
      SELECT couple_id FROM couples 
      WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
    )
  );
