-- Add missing columns to existing counselor_profiles table
ALTER TABLE counselor_profiles
ADD COLUMN IF NOT EXISTS availability TEXT;

-- Rename years_experience to experience_years for consistency
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'counselor_profiles'
               AND column_name = 'years_experience') THEN
        ALTER TABLE counselor_profiles RENAME COLUMN years_experience TO experience_years;
    END IF;
END $$;

-- Add missing columns to existing counselor_couple_assignments table
ALTER TABLE counselor_couple_assignments
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add unique constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint
                   WHERE conname = 'counselor_couple_assignments_couple_id_key') THEN
        ALTER TABLE counselor_couple_assignments ADD CONSTRAINT counselor_couple_assignments_couple_id_key UNIQUE(couple_id);
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_counselor_profiles_specialization 
ON counselor_profiles(specialization);

CREATE INDEX IF NOT EXISTS idx_counselor_couple_assignments_counselor 
ON counselor_couple_assignments(counselor_id);

CREATE INDEX IF NOT EXISTS idx_counselor_couple_assignments_couple 
ON counselor_couple_assignments(couple_id);

-- Sample data will be added manually through the application
-- since we need real auth.users entries for foreign key constraints
