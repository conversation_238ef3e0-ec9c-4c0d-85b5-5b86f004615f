-- Fix RLS policy for couple_invitation_codes
-- Run this in Supabase SQL Editor

-- Drop existing policy
DROP POLICY IF EXISTS "Users can view their own codes" ON couple_invitation_codes;

-- Create new policy that allows reading active codes for validation
CREATE POLICY "Users can view active codes for validation"
  ON couple_invitation_codes FOR SELECT
  USING (
    -- Users can see their own codes (created or used)
    auth.uid() = creator_user_id 
    OR auth.uid() = used_by_user_id 
    -- OR anyone can see active codes for validation (but only basic info)
    OR (is_active = true AND expires_at > NOW())
  );

-- Alternative: Create a more restrictive policy that only allows reading code field
-- Uncomment this if you want more security:
/*
DROP POLICY IF EXISTS "Users can view active codes for validation" ON couple_invitation_codes;

CREATE POLICY "Users can view own codes"
  ON couple_invitation_codes FOR SELECT
  USING (auth.uid() = creator_user_id OR auth.uid() = used_by_user_id);

CREATE POLICY "Users can validate active codes"
  ON couple_invitation_codes FOR SELECT
  USING (is_active = true AND expires_at > NOW());
*/
