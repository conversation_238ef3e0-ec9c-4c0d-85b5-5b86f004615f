-- Fix UPDATE policy for couple_invitation_codes
-- Run this in Supabase SQL Editor

-- Check current policies
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual
FROM pg_policies 
WHERE tablename = 'couple_invitation_codes';

-- Drop and recreate UPDATE policy to allow users to mark codes as used
DROP POLICY IF EXISTS "Users can update codes they use" ON couple_invitation_codes;

CREATE POLICY "Users can update invitation codes"
  ON couple_invitation_codes FOR UPDATE
  USING (
    -- Creator can update their own codes
    auth.uid() = creator_user_id 
    -- OR anyone can update active codes to mark them as used (for connection)
    OR (is_active = true AND expires_at > NOW())
  )
  WITH CHECK (
    -- Same conditions for the updated row
    auth.uid() = creator_user_id 
    OR auth.uid() = used_by_user_id
  );

-- Verify the policy was created
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual
FROM pg_policies 
WHERE tablename = 'couple_invitation_codes' AND cmd = 'UPDATE';
