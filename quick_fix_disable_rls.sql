-- QUICK FIX: Temporarily disable <PERSON><PERSON> for testing
-- This will allow couple connection to work immediately
-- R<PERSON><PERSON>BER TO RE-ENABLE LATER!

-- Disable R<PERSON> on problematic tables
ALTER TABLE couples DISABLE ROW LEVEL SECURITY;
ALTER TABLE individual_results DISABLE ROW LEVEL SECURITY;
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Keep RLS enabled only for couple_invitation_codes (it's working)
-- ALTER TABLE couple_invitation_codes DISABLE ROW LEVEL SECURITY;

-- Check status
SELECT 
  schemaname, 
  tablename, 
  rowsecurity 
FROM pg_tables 
WHERE tablename IN ('couples', 'individual_results', 'profiles', 'couple_invitation_codes')
ORDER BY tablename;

-- TO RE-ENABLE LATER (after testing is complete):
/*
ALTER TABLE couples ENABLE ROW LEVEL SECURITY;
ALTER TABLE individual_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
*/
