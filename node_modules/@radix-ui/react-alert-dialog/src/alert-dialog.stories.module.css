.trigger {
}

.overlay,
.overlayAttr {
  /* ensures overlay is positionned correctly */
  position: fixed;
  inset: 0;
  /* --------- */
  background-color: var(--gray-12);
  opacity: 0.2;
}

.content,
.chromaticContent,
.contentAttr {
  /* ensures good default position for content */
  position: fixed;
  top: 0;
  left: 0;
  /* --------- */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--gray-1);
  min-width: 300px;
  min-height: 150px;
  padding: 50px;
  border-radius: 10px;
  background-color: var(--gray-1);
  box-shadow: 0 2px 10px var(--black-a6);
}

.cancel,
.action {
  appearance: none;
  padding: 10px;
  border: none;
}

.cancel {
  background: var(--gray-3);
  color: var(--gray-12);
}

.action {
  background: var(--red-9);
  color: var(--gray-1);
}

.title {
}

.description {
}

.chromaticContent {
  padding: 10px;
  min-width: auto;
  min-height: auto;
}

.triggerAttr .overlayAttr,
.contentAttr,
.cancelAttr,
.actionAttr,
.titleAttr,
.descriptionAttr {
  background-color: var(--blue-a12);
  border: 2px solid var(--blue-9);
  padding: 10px;
  &[data-state='closed'] {
    border-color: var(--red-9);
  }
  &[data-state='open'] {
    border-color: var(--green-9);
  }
}
