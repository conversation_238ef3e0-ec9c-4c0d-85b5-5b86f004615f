{"version": 3, "sources": ["../src/form.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Label as LabelPrimitive } from '@radix-ui/react-label';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype ScopedProps<P> = P & { __scopeForm?: Scope };\nconst [createFormContext, createFormScope] = createContextScope('Form');\n\n/* -------------------------------------------------------------------------------------------------\n * Form\n * -----------------------------------------------------------------------------------------------*/\n\nconst FORM_NAME = 'Form';\n\ntype ValidityMap = { [fieldName: string]: ValidityState | undefined };\ntype CustomMatcherEntriesMap = { [fieldName: string]: CustomMatcherEntry[] };\ntype CustomErrorsMap = { [fieldName: string]: Record<string, boolean> };\n\ntype ValidationContextValue = {\n  getFieldValidity(fieldName: string): ValidityState | undefined;\n  onFieldValidityChange(fieldName: string, validity: ValidityState): void;\n\n  getFieldCustomMatcherEntries(fieldName: string): CustomMatcherEntry[];\n  onFieldCustomMatcherEntryAdd(fieldName: string, matcherEntry: CustomMatcherEntry): void;\n  onFieldCustomMatcherEntryRemove(fieldName: string, matcherEntryId: string): void;\n\n  getFieldCustomErrors(fieldName: string): Record<string, boolean>;\n  onFieldCustomErrorsChange(fieldName: string, errors: Record<string, boolean>): void;\n\n  onFieldValiditionClear(fieldName: string): void;\n};\nconst [ValidationProvider, useValidationContext] =\n  createFormContext<ValidationContextValue>(FORM_NAME);\n\ntype MessageIdsMap = { [fieldName: string]: Set<string> };\n\ntype AriaDescriptionContextValue = {\n  onFieldMessageIdAdd(fieldName: string, id: string): void;\n  onFieldMessageIdRemove(fieldName: string, id: string): void;\n  getFieldDescription(fieldName: string): string | undefined;\n};\nconst [AriaDescriptionProvider, useAriaDescriptionContext] =\n  createFormContext<AriaDescriptionContextValue>(FORM_NAME);\n\ntype FormElement = React.ElementRef<typeof Primitive.form>;\ntype PrimitiveFormProps = React.ComponentPropsWithoutRef<typeof Primitive.form>;\ninterface FormProps extends PrimitiveFormProps {\n  onClearServerErrors?(): void;\n}\n\nconst Form = React.forwardRef<FormElement, FormProps>(\n  (props: ScopedProps<FormProps>, forwardedRef) => {\n    const { __scopeForm, onClearServerErrors = () => {}, ...rootProps } = props;\n    const formRef = React.useRef<HTMLFormElement>(null);\n    const composedFormRef = useComposedRefs(forwardedRef, formRef);\n\n    // native validity per field\n    const [validityMap, setValidityMap] = React.useState<ValidityMap>({});\n    const getFieldValidity: ValidationContextValue['getFieldValidity'] = React.useCallback(\n      (fieldName) => validityMap[fieldName],\n      [validityMap]\n    );\n    const handleFieldValidityChange: ValidationContextValue['onFieldValidityChange'] =\n      React.useCallback(\n        (fieldName, validity) =>\n          setValidityMap((prevValidityMap) => ({\n            ...prevValidityMap,\n            [fieldName]: { ...(prevValidityMap[fieldName] ?? {}), ...validity },\n          })),\n        []\n      );\n    const handleFieldValiditionClear: ValidationContextValue['onFieldValiditionClear'] =\n      React.useCallback((fieldName) => {\n        setValidityMap((prevValidityMap) => ({ ...prevValidityMap, [fieldName]: undefined }));\n        setCustomErrorsMap((prevCustomErrorsMap) => ({ ...prevCustomErrorsMap, [fieldName]: {} }));\n      }, []);\n\n    // custom matcher entries per field\n    const [customMatcherEntriesMap, setCustomMatcherEntriesMap] =\n      React.useState<CustomMatcherEntriesMap>({});\n    const getFieldCustomMatcherEntries: ValidationContextValue['getFieldCustomMatcherEntries'] =\n      React.useCallback(\n        (fieldName) => customMatcherEntriesMap[fieldName] ?? [],\n        [customMatcherEntriesMap]\n      );\n    const handleFieldCustomMatcherAdd: ValidationContextValue['onFieldCustomMatcherEntryAdd'] =\n      React.useCallback((fieldName, matcherEntry) => {\n        setCustomMatcherEntriesMap((prevCustomMatcherEntriesMap) => ({\n          ...prevCustomMatcherEntriesMap,\n          [fieldName]: [...(prevCustomMatcherEntriesMap[fieldName] ?? []), matcherEntry],\n        }));\n      }, []);\n    const handleFieldCustomMatcherRemove: ValidationContextValue['onFieldCustomMatcherEntryRemove'] =\n      React.useCallback((fieldName, matcherEntryId) => {\n        setCustomMatcherEntriesMap((prevCustomMatcherEntriesMap) => ({\n          ...prevCustomMatcherEntriesMap,\n          [fieldName]: (prevCustomMatcherEntriesMap[fieldName] ?? []).filter(\n            (matcherEntry) => matcherEntry.id !== matcherEntryId\n          ),\n        }));\n      }, []);\n\n    // custom errors per field\n    const [customErrorsMap, setCustomErrorsMap] = React.useState<CustomErrorsMap>({});\n    const getFieldCustomErrors: ValidationContextValue['getFieldCustomErrors'] = React.useCallback(\n      (fieldName) => customErrorsMap[fieldName] ?? {},\n      [customErrorsMap]\n    );\n    const handleFieldCustomErrorsChange: ValidationContextValue['onFieldCustomErrorsChange'] =\n      React.useCallback((fieldName, customErrors) => {\n        setCustomErrorsMap((prevCustomErrorsMap) => ({\n          ...prevCustomErrorsMap,\n          [fieldName]: { ...(prevCustomErrorsMap[fieldName] ?? {}), ...customErrors },\n        }));\n      }, []);\n\n    // messageIds per field\n    const [messageIdsMap, setMessageIdsMap] = React.useState<MessageIdsMap>({});\n    const handleFieldMessageIdAdd: AriaDescriptionContextValue['onFieldMessageIdAdd'] =\n      React.useCallback((fieldName, id) => {\n        setMessageIdsMap((prevMessageIdsMap) => {\n          const fieldDescriptionIds = new Set(prevMessageIdsMap[fieldName]).add(id);\n          return { ...prevMessageIdsMap, [fieldName]: fieldDescriptionIds };\n        });\n      }, []);\n    const handleFieldMessageIdRemove: AriaDescriptionContextValue['onFieldMessageIdRemove'] =\n      React.useCallback((fieldName, id) => {\n        setMessageIdsMap((prevMessageIdsMap) => {\n          const fieldDescriptionIds = new Set(prevMessageIdsMap[fieldName]);\n          fieldDescriptionIds.delete(id);\n          return { ...prevMessageIdsMap, [fieldName]: fieldDescriptionIds };\n        });\n      }, []);\n    const getFieldDescription: AriaDescriptionContextValue['getFieldDescription'] =\n      React.useCallback(\n        (fieldName) => Array.from(messageIdsMap[fieldName] ?? []).join(' ') || undefined,\n        [messageIdsMap]\n      );\n\n    return (\n      <ValidationProvider\n        scope={__scopeForm}\n        getFieldValidity={getFieldValidity}\n        onFieldValidityChange={handleFieldValidityChange}\n        getFieldCustomMatcherEntries={getFieldCustomMatcherEntries}\n        onFieldCustomMatcherEntryAdd={handleFieldCustomMatcherAdd}\n        onFieldCustomMatcherEntryRemove={handleFieldCustomMatcherRemove}\n        getFieldCustomErrors={getFieldCustomErrors}\n        onFieldCustomErrorsChange={handleFieldCustomErrorsChange}\n        onFieldValiditionClear={handleFieldValiditionClear}\n      >\n        <AriaDescriptionProvider\n          scope={__scopeForm}\n          onFieldMessageIdAdd={handleFieldMessageIdAdd}\n          onFieldMessageIdRemove={handleFieldMessageIdRemove}\n          getFieldDescription={getFieldDescription}\n        >\n          <Primitive.form\n            {...rootProps}\n            ref={composedFormRef}\n            // focus first invalid control when the form is submitted\n            onInvalid={composeEventHandlers(props.onInvalid, (event) => {\n              const firstInvalidControl = getFirstInvalidControl(event.currentTarget);\n              if (firstInvalidControl === event.target) firstInvalidControl.focus();\n\n              // prevent default browser UI for form validation\n              event.preventDefault();\n            })}\n            // clear server errors when the form is re-submitted\n            onSubmit={composeEventHandlers(props.onSubmit, onClearServerErrors, {\n              checkForDefaultPrevented: false,\n            })}\n            // clear server errors when the form is reset\n            onReset={composeEventHandlers(props.onReset, onClearServerErrors)}\n          />\n        </AriaDescriptionProvider>\n      </ValidationProvider>\n    );\n  }\n);\n\nForm.displayName = FORM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormField\n * -----------------------------------------------------------------------------------------------*/\n\nconst FIELD_NAME = 'FormField';\n\ntype FormFieldContextValue = {\n  id: string;\n  name: string;\n  serverInvalid: boolean;\n};\nconst [FormFieldProvider, useFormFieldContext] =\n  createFormContext<FormFieldContextValue>(FIELD_NAME);\n\ntype FormFieldElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FormFieldProps extends PrimitiveDivProps {\n  name: string;\n  serverInvalid?: boolean;\n}\n\nconst FormField = React.forwardRef<FormFieldElement, FormFieldProps>(\n  (props: ScopedProps<FormFieldProps>, forwardedRef) => {\n    const { __scopeForm, name, serverInvalid = false, ...fieldProps } = props;\n    const validationContext = useValidationContext(FIELD_NAME, __scopeForm);\n    const validity = validationContext.getFieldValidity(name);\n    const id = useId();\n\n    return (\n      <FormFieldProvider scope={__scopeForm} id={id} name={name} serverInvalid={serverInvalid}>\n        <Primitive.div\n          data-valid={getValidAttribute(validity, serverInvalid)}\n          data-invalid={getInvalidAttribute(validity, serverInvalid)}\n          {...fieldProps}\n          ref={forwardedRef}\n        />\n      </FormFieldProvider>\n    );\n  }\n);\n\nFormField.displayName = FIELD_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'FormLabel';\n\ntype FormLabelElement = React.ElementRef<typeof LabelPrimitive>;\ntype LabelProps = React.ComponentPropsWithoutRef<typeof LabelPrimitive>;\ninterface FormLabelProps extends LabelProps {}\n\nconst FormLabel = React.forwardRef<FormLabelElement, FormLabelProps>(\n  (props: ScopedProps<FormLabelProps>, forwardedRef) => {\n    const { __scopeForm, ...labelProps } = props;\n    const validationContext = useValidationContext(LABEL_NAME, __scopeForm);\n    const fieldContext = useFormFieldContext(LABEL_NAME, __scopeForm);\n    const htmlFor = labelProps.htmlFor || fieldContext.id;\n    const validity = validationContext.getFieldValidity(fieldContext.name);\n\n    return (\n      <LabelPrimitive\n        data-valid={getValidAttribute(validity, fieldContext.serverInvalid)}\n        data-invalid={getInvalidAttribute(validity, fieldContext.serverInvalid)}\n        {...labelProps}\n        ref={forwardedRef}\n        htmlFor={htmlFor}\n      />\n    );\n  }\n);\n\nFormLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormControl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTROL_NAME = 'FormControl';\n\ntype FormControlElement = React.ElementRef<typeof Primitive.input>;\ntype PrimitiveInputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface FormControlProps extends PrimitiveInputProps {}\n\nconst FormControl = React.forwardRef<FormControlElement, FormControlProps>(\n  (props: ScopedProps<FormControlProps>, forwardedRef) => {\n    const { __scopeForm, ...controlProps } = props;\n\n    const validationContext = useValidationContext(CONTROL_NAME, __scopeForm);\n    const fieldContext = useFormFieldContext(CONTROL_NAME, __scopeForm);\n    const ariaDescriptionContext = useAriaDescriptionContext(CONTROL_NAME, __scopeForm);\n\n    const ref = React.useRef<FormControlElement>(null);\n    const composedRef = useComposedRefs(forwardedRef, ref);\n    const name = controlProps.name || fieldContext.name;\n    const id = controlProps.id || fieldContext.id;\n    const customMatcherEntries = validationContext.getFieldCustomMatcherEntries(name);\n\n    const { onFieldValidityChange, onFieldCustomErrorsChange, onFieldValiditionClear } =\n      validationContext;\n    const updateControlValidity = React.useCallback(\n      async (control: FormControlElement) => {\n        //------------------------------------------------------------------------------------------\n        // 1. first, if we have built-in errors we stop here\n\n        if (hasBuiltInError(control.validity)) {\n          const controlValidity = validityStateToObject(control.validity);\n          onFieldValidityChange(name, controlValidity);\n          return;\n        }\n\n        //------------------------------------------------------------------------------------------\n        // 2. then gather the form data to give to custom matchers for cross-comparisons\n\n        const formData = control.form ? new FormData(control.form) : new FormData();\n        const matcherArgs: CustomMatcherArgs = [control.value, formData];\n\n        //------------------------------------------------------------------------------------------\n        // 3. split sync and async custom matcher entries\n\n        const syncCustomMatcherEntries: Array<SyncCustomMatcherEntry> = [];\n        const ayncCustomMatcherEntries: Array<AsyncCustomMatcherEntry> = [];\n        customMatcherEntries.forEach((customMatcherEntry) => {\n          if (isAsyncCustomMatcherEntry(customMatcherEntry, matcherArgs)) {\n            ayncCustomMatcherEntries.push(customMatcherEntry);\n          } else if (isSyncCustomMatcherEntry(customMatcherEntry)) {\n            syncCustomMatcherEntries.push(customMatcherEntry);\n          }\n        });\n\n        //------------------------------------------------------------------------------------------\n        // 4. run sync custom matchers and update control validity / internal validity + errors\n\n        const syncCustomErrors = syncCustomMatcherEntries.map(({ id, match }) => {\n          return [id, match(...matcherArgs)] as const;\n        });\n        const syncCustomErrorsById = Object.fromEntries(syncCustomErrors);\n        const hasSyncCustomErrors = Object.values(syncCustomErrorsById).some(Boolean);\n        const hasCustomError = hasSyncCustomErrors;\n        control.setCustomValidity(hasCustomError ? DEFAULT_INVALID_MESSAGE : '');\n        const controlValidity = validityStateToObject(control.validity);\n        onFieldValidityChange(name, controlValidity);\n        onFieldCustomErrorsChange(name, syncCustomErrorsById);\n\n        //------------------------------------------------------------------------------------------\n        // 5. run async custom matchers and update control validity / internal validity + errors\n\n        if (!hasSyncCustomErrors && ayncCustomMatcherEntries.length > 0) {\n          const promisedCustomErrors = ayncCustomMatcherEntries.map(({ id, match }) =>\n            match(...matcherArgs).then((matches) => [id, matches] as const)\n          );\n          const asyncCustomErrors = await Promise.all(promisedCustomErrors);\n          const asyncCustomErrorsById = Object.fromEntries(asyncCustomErrors);\n          const hasAsyncCustomErrors = Object.values(asyncCustomErrorsById).some(Boolean);\n          const hasCustomError = hasAsyncCustomErrors;\n          control.setCustomValidity(hasCustomError ? DEFAULT_INVALID_MESSAGE : '');\n          const controlValidity = validityStateToObject(control.validity);\n          onFieldValidityChange(name, controlValidity);\n          onFieldCustomErrorsChange(name, asyncCustomErrorsById);\n        }\n      },\n      [customMatcherEntries, name, onFieldCustomErrorsChange, onFieldValidityChange]\n    );\n\n    React.useEffect(() => {\n      const control = ref.current;\n      if (control) {\n        // We only want validate on change (native `change` event, not React's `onChange`). This is primarily\n        // a UX decision, we don't want to validate on every keystroke and React's `onChange` is the `input` event.\n        const handleChange = () => updateControlValidity(control);\n        control.addEventListener('change', handleChange);\n        return () => control.removeEventListener('change', handleChange);\n      }\n    }, [updateControlValidity]);\n\n    const resetControlValidity = React.useCallback(() => {\n      const control = ref.current;\n      if (control) {\n        control.setCustomValidity('');\n        onFieldValiditionClear(name);\n      }\n    }, [name, onFieldValiditionClear]);\n\n    // reset validity and errors when the form is reset\n    React.useEffect(() => {\n      const form = ref.current?.form;\n      if (form) {\n        form.addEventListener('reset', resetControlValidity);\n        return () => form.removeEventListener('reset', resetControlValidity);\n      }\n    }, [resetControlValidity]);\n\n    // focus first invalid control when fields are set as invalid by server\n    React.useEffect(() => {\n      const control = ref.current;\n      const form = control?.closest('form');\n      if (form && fieldContext.serverInvalid) {\n        const firstInvalidControl = getFirstInvalidControl(form);\n        if (firstInvalidControl === control) firstInvalidControl.focus();\n      }\n    }, [fieldContext.serverInvalid]);\n\n    const validity = validationContext.getFieldValidity(name);\n\n    return (\n      <Primitive.input\n        data-valid={getValidAttribute(validity, fieldContext.serverInvalid)}\n        data-invalid={getInvalidAttribute(validity, fieldContext.serverInvalid)}\n        aria-invalid={fieldContext.serverInvalid ? true : undefined}\n        aria-describedby={ariaDescriptionContext.getFieldDescription(name)}\n        // disable default browser behaviour of showing built-in error message on hover\n        title=\"\"\n        {...controlProps}\n        ref={composedRef}\n        id={id}\n        name={name}\n        onInvalid={composeEventHandlers(props.onInvalid, (event) => {\n          const control = event.currentTarget;\n          updateControlValidity(control);\n        })}\n        onChange={composeEventHandlers(props.onChange, (_event) => {\n          // reset validity when user changes value\n          resetControlValidity();\n        })}\n      />\n    );\n  }\n);\n\nFormControl.displayName = CONTROL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormMessage\n * -----------------------------------------------------------------------------------------------*/\n\nconst _validityMatchers = [\n  'badInput',\n  'patternMismatch',\n  'rangeOverflow',\n  'rangeUnderflow',\n  'stepMismatch',\n  'tooLong',\n  'tooShort',\n  'typeMismatch',\n  'valid',\n  'valueMissing',\n] as const;\ntype ValidityMatcher = (typeof _validityMatchers)[number];\n\nconst DEFAULT_INVALID_MESSAGE = 'This value is not valid';\nconst DEFAULT_BUILT_IN_MESSAGES: Record<ValidityMatcher, string | undefined> = {\n  badInput: DEFAULT_INVALID_MESSAGE,\n  patternMismatch: 'This value does not match the required pattern',\n  rangeOverflow: 'This value is too large',\n  rangeUnderflow: 'This value is too small',\n  stepMismatch: 'This value does not match the required step',\n  tooLong: 'This value is too long',\n  tooShort: 'This value is too short',\n  typeMismatch: 'This value does not match the required type',\n  valid: undefined,\n  valueMissing: 'This value is missing',\n};\n\nconst MESSAGE_NAME = 'FormMessage';\n\ntype FormMessageElement = FormMessageImplElement;\ninterface FormMessageProps extends Omit<FormMessageImplProps, 'name'> {\n  match?: ValidityMatcher | CustomMatcher;\n  forceMatch?: boolean;\n  name?: string;\n}\n\nconst FormMessage = React.forwardRef<FormMessageElement, FormMessageProps>(\n  (props: ScopedProps<FormMessageProps>, forwardedRef) => {\n    const { match, name: nameProp, ...messageProps } = props;\n    const fieldContext = useFormFieldContext(MESSAGE_NAME, props.__scopeForm);\n    const name = nameProp ?? fieldContext.name;\n\n    if (match === undefined) {\n      return (\n        <FormMessageImpl {...messageProps} ref={forwardedRef} name={name}>\n          {props.children || DEFAULT_INVALID_MESSAGE}\n        </FormMessageImpl>\n      );\n    } else if (typeof match === 'function') {\n      return <FormCustomMessage match={match} {...messageProps} ref={forwardedRef} name={name} />;\n    } else {\n      return <FormBuiltInMessage match={match} {...messageProps} ref={forwardedRef} name={name} />;\n    }\n  }\n);\n\nFormMessage.displayName = MESSAGE_NAME;\n\ntype FormBuiltInMessageElement = FormMessageImplElement;\ninterface FormBuiltInMessageProps extends FormMessageImplProps {\n  match: ValidityMatcher;\n  forceMatch?: boolean;\n  name: string;\n}\n\nconst FormBuiltInMessage = React.forwardRef<FormBuiltInMessageElement, FormBuiltInMessageProps>(\n  (props: ScopedProps<FormBuiltInMessageProps>, forwardedRef) => {\n    const { match, forceMatch = false, name, children, ...messageProps } = props;\n    const validationContext = useValidationContext(MESSAGE_NAME, messageProps.__scopeForm);\n    const validity = validationContext.getFieldValidity(name);\n    const matches = forceMatch || validity?.[match];\n\n    if (matches) {\n      return (\n        <FormMessageImpl ref={forwardedRef} {...messageProps} name={name}>\n          {children ?? DEFAULT_BUILT_IN_MESSAGES[match]}\n        </FormMessageImpl>\n      );\n    }\n\n    return null;\n  }\n);\n\ntype FormCustomMessageElement = React.ElementRef<typeof FormMessageImpl>;\ninterface FormCustomMessageProps extends React.ComponentPropsWithoutRef<typeof FormMessageImpl> {\n  match: CustomMatcher;\n  forceMatch?: boolean;\n  name: string;\n}\n\nconst FormCustomMessage = React.forwardRef<FormCustomMessageElement, FormCustomMessageProps>(\n  (props: ScopedProps<FormCustomMessageProps>, forwardedRef) => {\n    const { match, forceMatch = false, name, id: idProp, children, ...messageProps } = props;\n    const validationContext = useValidationContext(MESSAGE_NAME, messageProps.__scopeForm);\n    const ref = React.useRef<FormCustomMessageElement>(null);\n    const composedRef = useComposedRefs(forwardedRef, ref);\n    const _id = useId();\n    const id = idProp ?? _id;\n\n    const customMatcherEntry = React.useMemo(() => ({ id, match }), [id, match]);\n    const { onFieldCustomMatcherEntryAdd, onFieldCustomMatcherEntryRemove } = validationContext;\n    React.useEffect(() => {\n      onFieldCustomMatcherEntryAdd(name, customMatcherEntry);\n      return () => onFieldCustomMatcherEntryRemove(name, customMatcherEntry.id);\n    }, [customMatcherEntry, name, onFieldCustomMatcherEntryAdd, onFieldCustomMatcherEntryRemove]);\n\n    const validity = validationContext.getFieldValidity(name);\n    const customErrors = validationContext.getFieldCustomErrors(name);\n    const hasMatchingCustomError = customErrors[id];\n    const matches =\n      forceMatch || (validity && !hasBuiltInError(validity) && hasMatchingCustomError);\n\n    if (matches) {\n      return (\n        <FormMessageImpl id={id} ref={composedRef} {...messageProps} name={name}>\n          {children ?? DEFAULT_INVALID_MESSAGE}\n        </FormMessageImpl>\n      );\n    }\n\n    return null;\n  }\n);\n\ntype FormMessageImplElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface FormMessageImplProps extends PrimitiveSpanProps {\n  name: string;\n}\n\nconst FormMessageImpl = React.forwardRef<FormMessageImplElement, FormMessageImplProps>(\n  (props: ScopedProps<FormMessageImplProps>, forwardedRef) => {\n    const { __scopeForm, id: idProp, name, ...messageProps } = props;\n    const ariaDescriptionContext = useAriaDescriptionContext(MESSAGE_NAME, __scopeForm);\n    const _id = useId();\n    const id = idProp ?? _id;\n\n    const { onFieldMessageIdAdd, onFieldMessageIdRemove } = ariaDescriptionContext;\n    React.useEffect(() => {\n      onFieldMessageIdAdd(name, id);\n      return () => onFieldMessageIdRemove(name, id);\n    }, [name, id, onFieldMessageIdAdd, onFieldMessageIdRemove]);\n\n    return <Primitive.span id={id} {...messageProps} ref={forwardedRef} />;\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * FormValidityState\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALIDITY_STATE_NAME = 'FormValidityState';\n\ninterface FormValidityStateProps {\n  children(validity: ValidityState | undefined): React.ReactNode;\n  name?: string;\n}\n\nconst FormValidityState = (props: ScopedProps<FormValidityStateProps>) => {\n  const { __scopeForm, name: nameProp, children } = props;\n  const validationContext = useValidationContext(VALIDITY_STATE_NAME, __scopeForm);\n  const fieldContext = useFormFieldContext(VALIDITY_STATE_NAME, __scopeForm);\n  const name = nameProp ?? fieldContext.name;\n  const validity = validationContext.getFieldValidity(name);\n  return <>{children(validity)}</>;\n};\n\nFormValidityState.displayName = VALIDITY_STATE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormSubmit\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUBMIT_NAME = 'FormSubmit';\n\ntype FormSubmitElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface FormSubmitProps extends PrimitiveButtonProps {}\n\nconst FormSubmit = React.forwardRef<FormSubmitElement, FormSubmitProps>(\n  (props: ScopedProps<FormSubmitProps>, forwardedRef) => {\n    const { __scopeForm, ...submitProps } = props;\n    return <Primitive.button type=\"submit\" {...submitProps} ref={forwardedRef} />;\n  }\n);\n\nFormSubmit.displayName = SUBMIT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ValidityStateKey = keyof ValidityState;\ntype SyncCustomMatcher = (value: string, formData: FormData) => boolean;\ntype AsyncCustomMatcher = (value: string, formData: FormData) => Promise<boolean>;\ntype CustomMatcher = SyncCustomMatcher | AsyncCustomMatcher;\ntype CustomMatcherEntry = { id: string; match: CustomMatcher };\ntype SyncCustomMatcherEntry = { id: string; match: SyncCustomMatcher };\ntype AsyncCustomMatcherEntry = { id: string; match: AsyncCustomMatcher };\ntype CustomMatcherArgs = [string, FormData];\n\nfunction validityStateToObject(validity: ValidityState) {\n  const object: any = {};\n  for (const key in validity) {\n    object[key] = validity[key as ValidityStateKey];\n  }\n  return object as Record<ValidityStateKey, boolean>;\n}\n\nfunction isHTMLElement(element: any): element is HTMLElement {\n  return element instanceof HTMLElement;\n}\n\nfunction isFormControl(element: any): element is { validity: ValidityState } {\n  return 'validity' in element;\n}\n\nfunction isInvalid(control: HTMLElement) {\n  return (\n    isFormControl(control) &&\n    (control.validity.valid === false || control.getAttribute('aria-invalid') === 'true')\n  );\n}\n\nfunction getFirstInvalidControl(form: HTMLFormElement): HTMLElement | undefined {\n  const elements = form.elements;\n  const [firstInvalidControl] = Array.from(elements).filter(isHTMLElement).filter(isInvalid);\n  return firstInvalidControl;\n}\n\nfunction isAsyncCustomMatcherEntry(\n  entry: CustomMatcherEntry,\n  args: CustomMatcherArgs\n): entry is AsyncCustomMatcherEntry {\n  return entry.match.constructor.name === 'AsyncFunction' || returnsPromise(entry.match, args);\n}\n\nfunction isSyncCustomMatcherEntry(entry: CustomMatcherEntry): entry is SyncCustomMatcherEntry {\n  return entry.match.constructor.name === 'Function';\n}\n\nfunction returnsPromise(func: Function, args: Array<unknown>) {\n  return func(...args) instanceof Promise;\n}\n\nfunction hasBuiltInError(validity: ValidityState) {\n  let error = false;\n  for (const validityKey in validity) {\n    const key = validityKey as ValidityStateKey;\n    if (key !== 'valid' && key !== 'customError' && validity[key]) {\n      error = true;\n      break;\n    }\n  }\n  return error;\n}\n\nfunction getValidAttribute(validity: ValidityState | undefined, serverInvalid: boolean) {\n  if (validity?.valid === true && !serverInvalid) return true;\n  return undefined;\n}\nfunction getInvalidAttribute(validity: ValidityState | undefined, serverInvalid: boolean) {\n  if (validity?.valid === false || serverInvalid) return true;\n  return undefined;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Form;\nconst Field = FormField;\nconst Label = FormLabel;\nconst Control = FormControl;\nconst Message = FormMessage;\nconst ValidityState = FormValidityState;\nconst Submit = FormSubmit;\n\nexport {\n  createFormScope,\n  //\n  Form,\n  FormField,\n  FormLabel,\n  FormControl,\n  FormMessage,\n  FormValidityState,\n  FormSubmit,\n  //\n  Root,\n  Field,\n  Label,\n  Control,\n  Message,\n  ValidityState,\n  Submit,\n};\n\nexport type {\n  FormProps,\n  FormFieldProps,\n  FormLabelProps,\n  FormControlProps,\n  FormMessageProps,\n  FormValidityStateProps,\n  FormSubmitProps,\n};\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,aAAa;AACtB,SAAS,SAAS,sBAAsB;AACxC,SAAS,iBAAiB;AA4JhB,SA4aD,UA5aC;AAvJV,IAAM,CAAC,mBAAmB,eAAe,IAAI,mBAAmB,MAAM;AAMtE,IAAM,YAAY;AAmBlB,IAAM,CAAC,oBAAoB,oBAAoB,IAC7C,kBAA0C,SAAS;AASrD,IAAM,CAAC,yBAAyB,yBAAyB,IACvD,kBAA+C,SAAS;AAQ1D,IAAM,OAAa;AAAA,EACjB,CAAC,OAA+B,iBAAiB;AAC/C,UAAM,EAAE,aAAa,sBAAsB,MAAM;AAAA,IAAC,GAAG,GAAG,UAAU,IAAI;AACtE,UAAM,UAAgB,aAAwB,IAAI;AAClD,UAAM,kBAAkB,gBAAgB,cAAc,OAAO;AAG7D,UAAM,CAAC,aAAa,cAAc,IAAU,eAAsB,CAAC,CAAC;AACpE,UAAM,mBAAqE;AAAA,MACzE,CAAC,cAAc,YAAY,SAAS;AAAA,MACpC,CAAC,WAAW;AAAA,IACd;AACA,UAAM,4BACE;AAAA,MACJ,CAAC,WAAW,aACV,eAAe,CAAC,qBAAqB;AAAA,QACnC,GAAG;AAAA,QACH,CAAC,SAAS,GAAG,EAAE,GAAI,gBAAgB,SAAS,KAAK,CAAC,GAAI,GAAG,SAAS;AAAA,MACpE,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AACF,UAAM,6BACE,kBAAY,CAAC,cAAc;AAC/B,qBAAe,CAAC,qBAAqB,EAAE,GAAG,iBAAiB,CAAC,SAAS,GAAG,OAAU,EAAE;AACpF,yBAAmB,CAAC,yBAAyB,EAAE,GAAG,qBAAqB,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE;AAAA,IAC3F,GAAG,CAAC,CAAC;AAGP,UAAM,CAAC,yBAAyB,0BAA0B,IAClD,eAAkC,CAAC,CAAC;AAC5C,UAAM,+BACE;AAAA,MACJ,CAAC,cAAc,wBAAwB,SAAS,KAAK,CAAC;AAAA,MACtD,CAAC,uBAAuB;AAAA,IAC1B;AACF,UAAM,8BACE,kBAAY,CAAC,WAAW,iBAAiB;AAC7C,iCAA2B,CAAC,iCAAiC;AAAA,QAC3D,GAAG;AAAA,QACH,CAAC,SAAS,GAAG,CAAC,GAAI,4BAA4B,SAAS,KAAK,CAAC,GAAI,YAAY;AAAA,MAC/E,EAAE;AAAA,IACJ,GAAG,CAAC,CAAC;AACP,UAAM,iCACE,kBAAY,CAAC,WAAW,mBAAmB;AAC/C,iCAA2B,CAAC,iCAAiC;AAAA,QAC3D,GAAG;AAAA,QACH,CAAC,SAAS,IAAI,4BAA4B,SAAS,KAAK,CAAC,GAAG;AAAA,UAC1D,CAAC,iBAAiB,aAAa,OAAO;AAAA,QACxC;AAAA,MACF,EAAE;AAAA,IACJ,GAAG,CAAC,CAAC;AAGP,UAAM,CAAC,iBAAiB,kBAAkB,IAAU,eAA0B,CAAC,CAAC;AAChF,UAAM,uBAA6E;AAAA,MACjF,CAAC,cAAc,gBAAgB,SAAS,KAAK,CAAC;AAAA,MAC9C,CAAC,eAAe;AAAA,IAClB;AACA,UAAM,gCACE,kBAAY,CAAC,WAAW,iBAAiB;AAC7C,yBAAmB,CAAC,yBAAyB;AAAA,QAC3C,GAAG;AAAA,QACH,CAAC,SAAS,GAAG,EAAE,GAAI,oBAAoB,SAAS,KAAK,CAAC,GAAI,GAAG,aAAa;AAAA,MAC5E,EAAE;AAAA,IACJ,GAAG,CAAC,CAAC;AAGP,UAAM,CAAC,eAAe,gBAAgB,IAAU,eAAwB,CAAC,CAAC;AAC1E,UAAM,0BACE,kBAAY,CAAC,WAAW,OAAO;AACnC,uBAAiB,CAAC,sBAAsB;AACtC,cAAM,sBAAsB,IAAI,IAAI,kBAAkB,SAAS,CAAC,EAAE,IAAI,EAAE;AACxE,eAAO,EAAE,GAAG,mBAAmB,CAAC,SAAS,GAAG,oBAAoB;AAAA,MAClE,CAAC;AAAA,IACH,GAAG,CAAC,CAAC;AACP,UAAM,6BACE,kBAAY,CAAC,WAAW,OAAO;AACnC,uBAAiB,CAAC,sBAAsB;AACtC,cAAM,sBAAsB,IAAI,IAAI,kBAAkB,SAAS,CAAC;AAChE,4BAAoB,OAAO,EAAE;AAC7B,eAAO,EAAE,GAAG,mBAAmB,CAAC,SAAS,GAAG,oBAAoB;AAAA,MAClE,CAAC;AAAA,IACH,GAAG,CAAC,CAAC;AACP,UAAM,sBACE;AAAA,MACJ,CAAC,cAAc,MAAM,KAAK,cAAc,SAAS,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK;AAAA,MACvE,CAAC,aAAa;AAAA,IAChB;AAEF,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA,uBAAuB;AAAA,QACvB;AAAA,QACA,8BAA8B;AAAA,QAC9B,iCAAiC;AAAA,QACjC;AAAA,QACA,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QAExB;AAAA,UAAC;AAAA;AAAA,YACC,OAAO;AAAA,YACP,qBAAqB;AAAA,YACrB,wBAAwB;AAAA,YACxB;AAAA,YAEA;AAAA,cAAC,UAAU;AAAA,cAAV;AAAA,gBACE,GAAG;AAAA,gBACJ,KAAK;AAAA,gBAEL,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,wBAAM,sBAAsB,uBAAuB,MAAM,aAAa;AACtE,sBAAI,wBAAwB,MAAM,OAAQ,qBAAoB,MAAM;AAGpE,wBAAM,eAAe;AAAA,gBACvB,CAAC;AAAA,gBAED,UAAU,qBAAqB,MAAM,UAAU,qBAAqB;AAAA,kBAClE,0BAA0B;AAAA,gBAC5B,CAAC;AAAA,gBAED,SAAS,qBAAqB,MAAM,SAAS,mBAAmB;AAAA;AAAA,YAClE;AAAA;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,KAAK,cAAc;AAMnB,IAAM,aAAa;AAOnB,IAAM,CAAC,mBAAmB,mBAAmB,IAC3C,kBAAyC,UAAU;AASrD,IAAM,YAAkB;AAAA,EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,MAAM,gBAAgB,OAAO,GAAG,WAAW,IAAI;AACpE,UAAM,oBAAoB,qBAAqB,YAAY,WAAW;AACtE,UAAM,WAAW,kBAAkB,iBAAiB,IAAI;AACxD,UAAM,KAAK,MAAM;AAEjB,WACE,oBAAC,qBAAkB,OAAO,aAAa,IAAQ,MAAY,eACzD;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACC,cAAY,kBAAkB,UAAU,aAAa;AAAA,QACrD,gBAAc,oBAAoB,UAAU,aAAa;AAAA,QACxD,GAAG;AAAA,QACJ,KAAK;AAAA;AAAA,IACP,GACF;AAAA,EAEJ;AACF;AAEA,UAAU,cAAc;AAMxB,IAAM,aAAa;AAMnB,IAAM,YAAkB;AAAA,EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,GAAG,WAAW,IAAI;AACvC,UAAM,oBAAoB,qBAAqB,YAAY,WAAW;AACtE,UAAM,eAAe,oBAAoB,YAAY,WAAW;AAChE,UAAM,UAAU,WAAW,WAAW,aAAa;AACnD,UAAM,WAAW,kBAAkB,iBAAiB,aAAa,IAAI;AAErE,WACE;AAAA,MAAC;AAAA;AAAA,QACC,cAAY,kBAAkB,UAAU,aAAa,aAAa;AAAA,QAClE,gBAAc,oBAAoB,UAAU,aAAa,aAAa;AAAA,QACrE,GAAG;AAAA,QACJ,KAAK;AAAA,QACL;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,UAAU,cAAc;AAMxB,IAAM,eAAe;AAMrB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,aAAa,GAAG,aAAa,IAAI;AAEzC,UAAM,oBAAoB,qBAAqB,cAAc,WAAW;AACxE,UAAM,eAAe,oBAAoB,cAAc,WAAW;AAClE,UAAM,yBAAyB,0BAA0B,cAAc,WAAW;AAElF,UAAM,MAAY,aAA2B,IAAI;AACjD,UAAM,cAAc,gBAAgB,cAAc,GAAG;AACrD,UAAM,OAAO,aAAa,QAAQ,aAAa;AAC/C,UAAM,KAAK,aAAa,MAAM,aAAa;AAC3C,UAAM,uBAAuB,kBAAkB,6BAA6B,IAAI;AAEhF,UAAM,EAAE,uBAAuB,2BAA2B,uBAAuB,IAC/E;AACF,UAAM,wBAA8B;AAAA,MAClC,OAAO,YAAgC;AAIrC,YAAI,gBAAgB,QAAQ,QAAQ,GAAG;AACrC,gBAAMA,mBAAkB,sBAAsB,QAAQ,QAAQ;AAC9D,gCAAsB,MAAMA,gBAAe;AAC3C;AAAA,QACF;AAKA,cAAM,WAAW,QAAQ,OAAO,IAAI,SAAS,QAAQ,IAAI,IAAI,IAAI,SAAS;AAC1E,cAAM,cAAiC,CAAC,QAAQ,OAAO,QAAQ;AAK/D,cAAM,2BAA0D,CAAC;AACjE,cAAM,2BAA2D,CAAC;AAClE,6BAAqB,QAAQ,CAAC,uBAAuB;AACnD,cAAI,0BAA0B,oBAAoB,WAAW,GAAG;AAC9D,qCAAyB,KAAK,kBAAkB;AAAA,UAClD,WAAW,yBAAyB,kBAAkB,GAAG;AACvD,qCAAyB,KAAK,kBAAkB;AAAA,UAClD;AAAA,QACF,CAAC;AAKD,cAAM,mBAAmB,yBAAyB,IAAI,CAAC,EAAE,IAAAC,KAAI,MAAM,MAAM;AACvE,iBAAO,CAACA,KAAI,MAAM,GAAG,WAAW,CAAC;AAAA,QACnC,CAAC;AACD,cAAM,uBAAuB,OAAO,YAAY,gBAAgB;AAChE,cAAM,sBAAsB,OAAO,OAAO,oBAAoB,EAAE,KAAK,OAAO;AAC5E,cAAM,iBAAiB;AACvB,gBAAQ,kBAAkB,iBAAiB,0BAA0B,EAAE;AACvE,cAAM,kBAAkB,sBAAsB,QAAQ,QAAQ;AAC9D,8BAAsB,MAAM,eAAe;AAC3C,kCAA0B,MAAM,oBAAoB;AAKpD,YAAI,CAAC,uBAAuB,yBAAyB,SAAS,GAAG;AAC/D,gBAAM,uBAAuB,yBAAyB;AAAA,YAAI,CAAC,EAAE,IAAAA,KAAI,MAAM,MACrE,MAAM,GAAG,WAAW,EAAE,KAAK,CAAC,YAAY,CAACA,KAAI,OAAO,CAAU;AAAA,UAChE;AACA,gBAAM,oBAAoB,MAAM,QAAQ,IAAI,oBAAoB;AAChE,gBAAM,wBAAwB,OAAO,YAAY,iBAAiB;AAClE,gBAAM,uBAAuB,OAAO,OAAO,qBAAqB,EAAE,KAAK,OAAO;AAC9E,gBAAMC,kBAAiB;AACvB,kBAAQ,kBAAkBA,kBAAiB,0BAA0B,EAAE;AACvE,gBAAMF,mBAAkB,sBAAsB,QAAQ,QAAQ;AAC9D,gCAAsB,MAAMA,gBAAe;AAC3C,oCAA0B,MAAM,qBAAqB;AAAA,QACvD;AAAA,MACF;AAAA,MACA,CAAC,sBAAsB,MAAM,2BAA2B,qBAAqB;AAAA,IAC/E;AAEA,IAAM,gBAAU,MAAM;AACpB,YAAM,UAAU,IAAI;AACpB,UAAI,SAAS;AAGX,cAAM,eAAe,MAAM,sBAAsB,OAAO;AACxD,gBAAQ,iBAAiB,UAAU,YAAY;AAC/C,eAAO,MAAM,QAAQ,oBAAoB,UAAU,YAAY;AAAA,MACjE;AAAA,IACF,GAAG,CAAC,qBAAqB,CAAC;AAE1B,UAAM,uBAA6B,kBAAY,MAAM;AACnD,YAAM,UAAU,IAAI;AACpB,UAAI,SAAS;AACX,gBAAQ,kBAAkB,EAAE;AAC5B,+BAAuB,IAAI;AAAA,MAC7B;AAAA,IACF,GAAG,CAAC,MAAM,sBAAsB,CAAC;AAGjC,IAAM,gBAAU,MAAM;AACpB,YAAM,OAAO,IAAI,SAAS;AAC1B,UAAI,MAAM;AACR,aAAK,iBAAiB,SAAS,oBAAoB;AACnD,eAAO,MAAM,KAAK,oBAAoB,SAAS,oBAAoB;AAAA,MACrE;AAAA,IACF,GAAG,CAAC,oBAAoB,CAAC;AAGzB,IAAM,gBAAU,MAAM;AACpB,YAAM,UAAU,IAAI;AACpB,YAAM,OAAO,SAAS,QAAQ,MAAM;AACpC,UAAI,QAAQ,aAAa,eAAe;AACtC,cAAM,sBAAsB,uBAAuB,IAAI;AACvD,YAAI,wBAAwB,QAAS,qBAAoB,MAAM;AAAA,MACjE;AAAA,IACF,GAAG,CAAC,aAAa,aAAa,CAAC;AAE/B,UAAM,WAAW,kBAAkB,iBAAiB,IAAI;AAExD,WACE;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACC,cAAY,kBAAkB,UAAU,aAAa,aAAa;AAAA,QAClE,gBAAc,oBAAoB,UAAU,aAAa,aAAa;AAAA,QACtE,gBAAc,aAAa,gBAAgB,OAAO;AAAA,QAClD,oBAAkB,uBAAuB,oBAAoB,IAAI;AAAA,QAEjE,OAAM;AAAA,QACL,GAAG;AAAA,QACJ,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,UAAU,MAAM;AACtB,gCAAsB,OAAO;AAAA,QAC/B,CAAC;AAAA,QACD,UAAU,qBAAqB,MAAM,UAAU,CAAC,WAAW;AAEzD,+BAAqB;AAAA,QACvB,CAAC;AAAA;AAAA,IACH;AAAA,EAEJ;AACF;AAEA,YAAY,cAAc;AAoB1B,IAAM,0BAA0B;AAChC,IAAM,4BAAyE;AAAA,EAC7E,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,OAAO;AAAA,EACP,cAAc;AAChB;AAEA,IAAM,eAAe;AASrB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,OAAO,MAAM,UAAU,GAAG,aAAa,IAAI;AACnD,UAAM,eAAe,oBAAoB,cAAc,MAAM,WAAW;AACxE,UAAM,OAAO,YAAY,aAAa;AAEtC,QAAI,UAAU,QAAW;AACvB,aACE,oBAAC,mBAAiB,GAAG,cAAc,KAAK,cAAc,MACnD,gBAAM,YAAY,yBACrB;AAAA,IAEJ,WAAW,OAAO,UAAU,YAAY;AACtC,aAAO,oBAAC,qBAAkB,OAAe,GAAG,cAAc,KAAK,cAAc,MAAY;AAAA,IAC3F,OAAO;AACL,aAAO,oBAAC,sBAAmB,OAAe,GAAG,cAAc,KAAK,cAAc,MAAY;AAAA,IAC5F;AAAA,EACF;AACF;AAEA,YAAY,cAAc;AAS1B,IAAM,qBAA2B;AAAA,EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,OAAO,aAAa,OAAO,MAAM,UAAU,GAAG,aAAa,IAAI;AACvE,UAAM,oBAAoB,qBAAqB,cAAc,aAAa,WAAW;AACrF,UAAM,WAAW,kBAAkB,iBAAiB,IAAI;AACxD,UAAM,UAAU,cAAc,WAAW,KAAK;AAE9C,QAAI,SAAS;AACX,aACE,oBAAC,mBAAgB,KAAK,cAAe,GAAG,cAAc,MACnD,sBAAY,0BAA0B,KAAK,GAC9C;AAAA,IAEJ;AAEA,WAAO;AAAA,EACT;AACF;AASA,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,OAAO,aAAa,OAAO,MAAM,IAAI,QAAQ,UAAU,GAAG,aAAa,IAAI;AACnF,UAAM,oBAAoB,qBAAqB,cAAc,aAAa,WAAW;AACrF,UAAM,MAAY,aAAiC,IAAI;AACvD,UAAM,cAAc,gBAAgB,cAAc,GAAG;AACrD,UAAM,MAAM,MAAM;AAClB,UAAM,KAAK,UAAU;AAErB,UAAM,qBAA2B,cAAQ,OAAO,EAAE,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC;AAC3E,UAAM,EAAE,8BAA8B,gCAAgC,IAAI;AAC1E,IAAM,gBAAU,MAAM;AACpB,mCAA6B,MAAM,kBAAkB;AACrD,aAAO,MAAM,gCAAgC,MAAM,mBAAmB,EAAE;AAAA,IAC1E,GAAG,CAAC,oBAAoB,MAAM,8BAA8B,+BAA+B,CAAC;AAE5F,UAAM,WAAW,kBAAkB,iBAAiB,IAAI;AACxD,UAAM,eAAe,kBAAkB,qBAAqB,IAAI;AAChE,UAAM,yBAAyB,aAAa,EAAE;AAC9C,UAAM,UACJ,cAAe,YAAY,CAAC,gBAAgB,QAAQ,KAAK;AAE3D,QAAI,SAAS;AACX,aACE,oBAAC,mBAAgB,IAAQ,KAAK,aAAc,GAAG,cAAc,MAC1D,sBAAY,yBACf;AAAA,IAEJ;AAEA,WAAO;AAAA,EACT;AACF;AAQA,IAAM,kBAAwB;AAAA,EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,aAAa,IAAI,QAAQ,MAAM,GAAG,aAAa,IAAI;AAC3D,UAAM,yBAAyB,0BAA0B,cAAc,WAAW;AAClF,UAAM,MAAM,MAAM;AAClB,UAAM,KAAK,UAAU;AAErB,UAAM,EAAE,qBAAqB,uBAAuB,IAAI;AACxD,IAAM,gBAAU,MAAM;AACpB,0BAAoB,MAAM,EAAE;AAC5B,aAAO,MAAM,uBAAuB,MAAM,EAAE;AAAA,IAC9C,GAAG,CAAC,MAAM,IAAI,qBAAqB,sBAAsB,CAAC;AAE1D,WAAO,oBAAC,UAAU,MAAV,EAAe,IAAS,GAAG,cAAc,KAAK,cAAc;AAAA,EACtE;AACF;AAMA,IAAM,sBAAsB;AAO5B,IAAM,oBAAoB,CAAC,UAA+C;AACxE,QAAM,EAAE,aAAa,MAAM,UAAU,SAAS,IAAI;AAClD,QAAM,oBAAoB,qBAAqB,qBAAqB,WAAW;AAC/E,QAAM,eAAe,oBAAoB,qBAAqB,WAAW;AACzE,QAAM,OAAO,YAAY,aAAa;AACtC,QAAM,WAAW,kBAAkB,iBAAiB,IAAI;AACxD,SAAO,gCAAG,mBAAS,QAAQ,GAAE;AAC/B;AAEA,kBAAkB,cAAc;AAMhC,IAAM,cAAc;AAMpB,IAAM,aAAmB;AAAA,EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,aAAa,GAAG,YAAY,IAAI;AACxC,WAAO,oBAAC,UAAU,QAAV,EAAiB,MAAK,UAAU,GAAG,aAAa,KAAK,cAAc;AAAA,EAC7E;AACF;AAEA,WAAW,cAAc;AAazB,SAAS,sBAAsB,UAAyB;AACtD,QAAM,SAAc,CAAC;AACrB,aAAW,OAAO,UAAU;AAC1B,WAAO,GAAG,IAAI,SAAS,GAAuB;AAAA,EAChD;AACA,SAAO;AACT;AAEA,SAAS,cAAc,SAAsC;AAC3D,SAAO,mBAAmB;AAC5B;AAEA,SAAS,cAAc,SAAsD;AAC3E,SAAO,cAAc;AACvB;AAEA,SAAS,UAAU,SAAsB;AACvC,SACE,cAAc,OAAO,MACpB,QAAQ,SAAS,UAAU,SAAS,QAAQ,aAAa,cAAc,MAAM;AAElF;AAEA,SAAS,uBAAuB,MAAgD;AAC9E,QAAM,WAAW,KAAK;AACtB,QAAM,CAAC,mBAAmB,IAAI,MAAM,KAAK,QAAQ,EAAE,OAAO,aAAa,EAAE,OAAO,SAAS;AACzF,SAAO;AACT;AAEA,SAAS,0BACP,OACA,MACkC;AAClC,SAAO,MAAM,MAAM,YAAY,SAAS,mBAAmB,eAAe,MAAM,OAAO,IAAI;AAC7F;AAEA,SAAS,yBAAyB,OAA4D;AAC5F,SAAO,MAAM,MAAM,YAAY,SAAS;AAC1C;AAEA,SAAS,eAAe,MAAgB,MAAsB;AAC5D,SAAO,KAAK,GAAG,IAAI,aAAa;AAClC;AAEA,SAAS,gBAAgB,UAAyB;AAChD,MAAI,QAAQ;AACZ,aAAW,eAAe,UAAU;AAClC,UAAM,MAAM;AACZ,QAAI,QAAQ,WAAW,QAAQ,iBAAiB,SAAS,GAAG,GAAG;AAC7D,cAAQ;AACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,UAAqC,eAAwB;AACtF,MAAI,UAAU,UAAU,QAAQ,CAAC,cAAe,QAAO;AACvD,SAAO;AACT;AACA,SAAS,oBAAoB,UAAqC,eAAwB;AACxF,MAAI,UAAU,UAAU,SAAS,cAAe,QAAO;AACvD,SAAO;AACT;AAIA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,SAAS;", "names": ["controlValidity", "id", "hasCustomError"]}