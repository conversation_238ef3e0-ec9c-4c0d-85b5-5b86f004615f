-- Manual update for existing invitation code
-- Run this in Supabase SQL Editor to fix the current test data

-- First, let's see the current state
SELECT 
  cic.id,
  cic.code,
  cic.creator_user_id,
  cic.used_by_user_id,
  cic.is_active,
  cic.used_at,
  c.couple_id,
  c.user_id_1,
  c.user_id_2,
  (SELECT email FROM auth.users WHERE id = c.user_id_1) as user1_email,
  (SELECT email FROM auth.users WHERE id = c.user_id_2) as user2_email
FROM couple_invitation_codes cic
LEFT JOIN couples c ON (c.user_id_1 = cic.creator_user_id OR c.user_id_2 = cic.creator_user_id)
ORDER BY cic.created_at DESC
LIMIT 5;

-- Update the invitation code to mark it as used
-- Replace 'YOUR_CODE_HERE' with the actual code from your test
UPDATE couple_invitation_codes 
SET 
  used_by_user_id = (
    SELECT user_id_2 
    FROM couples 
    WHERE user_id_1 = couple_invitation_codes.creator_user_id 
    LIMIT 1
  ),
  used_at = NOW(),
  is_active = false
WHERE code = 'YOUR_CODE_HERE' 
AND used_by_user_id IS NULL;

-- Verify the update
SELECT 
  cic.id,
  cic.code,
  cic.creator_user_id,
  cic.used_by_user_id,
  cic.is_active,
  cic.used_at,
  (SELECT email FROM auth.users WHERE id = cic.creator_user_id) as creator_email,
  (SELECT email FROM auth.users WHERE id = cic.used_by_user_id) as used_by_email
FROM couple_invitation_codes cic
WHERE code = 'YOUR_CODE_HERE';
