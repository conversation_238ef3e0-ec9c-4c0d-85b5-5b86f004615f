-- Verification script - run this after migration to check if tables were created successfully

-- Check if profiles table exists and has correct structure
SELECT 
  table_name, 
  column_name, 
  data_type, 
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- Check if couple_invitation_codes table exists and has correct structure  
SELECT 
  table_name, 
  column_name, 
  data_type, 
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'couple_invitation_codes' 
ORDER BY ordinal_position;

-- Check RLS policies for profiles
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual
FROM pg_policies 
WHERE tablename = 'profiles';

-- Check RLS policies for couple_invitation_codes
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual
FROM pg_policies 
WHERE tablename = 'couple_invitation_codes';

-- Check indexes
SELECT 
  indexname, 
  tablename, 
  indexdef
FROM pg_indexes 
WHERE tablename IN ('profiles', 'couple_invitation_codes', 'couples')
ORDER BY tablename, indexname;
