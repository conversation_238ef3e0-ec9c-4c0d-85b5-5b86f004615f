{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94b516f0d85cfda4-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Fri, 06 Jun 2025 04:15:37 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=Y3DFHQgJCR5t1QcfISFx1lQKncGPFXXajEazEJO0Fd0-1749183337-*******-WvXAYxV.ZPArTey8O9aj6WbWVUgIwQZIxqIv0dP9rIld9_Y4.HC5iv6oq.GsJaqJhjiw6qw1Zxo7JdO3N6fahaZ.rGacNcshegrmi.JIkSk; path=/; expires=Fri, 06-Jun-25 04:45:37 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "4"}, "body": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}