{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94adcc187e293f7a-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Thu, 05 Jun 2025 07:01:11 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=SghncJhW02tjoSekbRr.L_LXYT6suXtJhngwHIIXmfg-1749106871-*******-eJCiUdKfDzA8IpjA0.AOj_vdtcmFNDlh6XUO8.jUXM7IGGE9fcbswxIK6id9uEKEd.fVhR.r1fE7a41bJZkGcNZvaJeXf6rHxnUI.G_5kOA; path=/; expires=Thu, 05-Jun-25 07:31:11 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "5"}, "body": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}