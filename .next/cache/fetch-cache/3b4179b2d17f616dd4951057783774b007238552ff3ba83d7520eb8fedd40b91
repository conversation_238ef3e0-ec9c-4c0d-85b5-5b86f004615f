{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94b51677bbb8df8f-CGK", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Fri, 06 Jun 2025 04:15:17 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=w0QOPKnEcNwQ_D6lDGb.XqMnM46RsC57WIUhvJCHH40-1749183317-*******-zFOu9p_AvKvmLp8OZCXTgmFAQHsk.iTT59FuHn4B4XFnwtdXAL73jPG9gegGBHH.gvMWLn4tGjxz7WnRqUqyV5oqsTHs92cUKlQCTbhqNo8; path=/; expires=Fri, 06-Jun-25 04:45:17 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "4"}, "body": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}