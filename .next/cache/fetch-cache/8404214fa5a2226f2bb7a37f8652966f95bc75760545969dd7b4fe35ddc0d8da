{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94acc14acec240a7-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Thu, 05 Jun 2025 03:59:02 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=1Jl0svwRmd.9QA.uQgVAegP_iTN1F48BqoBdYdgIRoI-1749095942-*******-SFwmGQ85GAP58RZhvXp_4_FmlgYGO5fcDg.7Fj0kj498kpODl_YXD.aKVXVEmJeciKHYhdYW7CXL3A3qW3i3MLLzM1f8R_9sEbSu8Z1Xmsc; path=/; expires=Thu, 05-Jun-25 04:29:02 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}