{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94adedf06ad7ab65-SIN", "connection": "close", "content-encoding": "br", "content-location": "/counselor_profiles?select=%2A", "content-profile": "public", "content-range": "0-2/3", "content-type": "application/json; charset=utf-8", "date": "Thu, 05 Jun 2025 07:24:17 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=l_mS_9GOX3jsZ3muOtCR9iRuJV3lnLhLF8nLpXsKMvM-1749108257-1.0.1.1-UQN9mxf1sUc5GneVscXswrx7WgeoSLSiQsvL9qHcY8PBU5b5lgyiGU7hgSwlL6se0LloPIVKBrSsqN3TL3u64ez21eYn6eVOqZDll23NPec; path=/; expires=Thu, 05-Jun-25 07:54:17 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/rest/v1/counselor_profiles?select=*"}, "revalidate": 31536000, "tags": []}