{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94acc14bffb340a7-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/profiles?id=eq.3dccb116-689f-4d35-86ee-096970fe9480&select=full_name%2Cemail", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Thu, 05 Jun 2025 03:59:03 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=8CUk8Ix._jSjzOjC3ZH2.8R5JM8nmMq9_cgNWJttGdU-1749095943-*******-.gtbkZbQ_pAGoZTbE2bPo63qfN7haaM6VvghipFWjHlo0Yiqb40yEb3h1.k7.s_1BIWgvSLm_qgZdHI5Oz553nC5F0Zzlkhx1lkSQhGx7kM; path=/; expires=Thu, 05-Jun-25 04:29:03 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "8"}, "body": "************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/rest/v1/profiles?select=full_name%2Cemail&id=eq.3dccb116-689f-4d35-86ee-096970fe9480"}, "revalidate": 31536000, "tags": []}