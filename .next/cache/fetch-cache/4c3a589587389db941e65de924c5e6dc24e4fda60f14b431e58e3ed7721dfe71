{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94adede93e05409c-SIN", "connection": "close", "content-encoding": "br", "content-location": "/couples?select=%2A", "content-profile": "public", "content-range": "0-0/1", "content-type": "application/json; charset=utf-8", "date": "Thu, 05 Jun 2025 07:24:16 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=Y7EODiUmUB4mQXBK60kx1tjthj_xZyloy6vabqctpM4-1749108256-1.0.1.1-CUvvnoEvtDcj9fHvkZn7c5PpA5Yc30.AiJNUDL1x2A0oJ7vgj8oRFQIMQFfK.tDf8cUrdYtvx5NM.8qbvDARHGm93LUwY0pVfXBRV1kjA8o; path=/; expires=Thu, 05-Jun-25 07:54:16 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "5"}, "body": "", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/rest/v1/couples?select=*"}, "revalidate": 31536000, "tags": []}