{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94adedf15ac0604d-SIN", "connection": "close", "content-encoding": "br", "content-location": "/individual_results?select=%2A", "content-profile": "public", "content-range": "0-0/1", "content-type": "application/json; charset=utf-8", "date": "Thu, 05 Jun 2025 07:24:17 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=F3YwwSIvUbhpvfHU3js2iHBiq9tmr1_tZMJYnx2HgyA-1749108257-1.0.1.1-oA3Lq0Rd9FKTU9JT0EpF0bKG1i4s21SLOOHPhA2fzw7xdZduTvhaLltYPmi5dhIxeD4P0HawKB6nVy8DgBri6TQhfZd9JSNjQvEO2gV2Q9k; path=/; expires=Thu, 05-Jun-25 07:54:17 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/rest/v1/individual_results?select=*"}, "revalidate": 31536000, "tags": []}