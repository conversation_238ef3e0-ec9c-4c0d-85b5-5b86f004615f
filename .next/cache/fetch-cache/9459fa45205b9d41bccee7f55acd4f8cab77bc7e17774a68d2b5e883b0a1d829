{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94adede7ec665f52-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Thu, 05 Jun 2025 07:24:16 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=fvVzRY56Pi8yuFDKGDo9ZlxEDaltqYFFEpBLxk6zcPs-1749108256-*******-glBoXuo2IkOq7m3w0tEk.YkJPbKeIOF7gBOGyUxMz.dUx.CL9z2WZHurHeUK4Q0_oVsoCdo0jhn_.IhKsXHIVvqBxyLLDc0seRH.bRipxhc; path=/; expires=Thu, 05-Jun-25 07:54:16 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "10"}, "body": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}