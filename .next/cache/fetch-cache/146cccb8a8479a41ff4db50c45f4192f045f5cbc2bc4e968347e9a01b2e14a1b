{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94adede85ca85f52-SIN", "connection": "close", "content-encoding": "br", "content-location": "/profiles?select=%2A", "content-profile": "public", "content-range": "0-4/5", "content-type": "application/json; charset=utf-8", "date": "Thu, 05 Jun 2025 07:24:16 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=NwFrF1QPv02SD36HqoNYWH.olSWXrSDrWYgaYVHcx.c-1749108256-1.0.1.1-lKcGJO9ed0PyKVw0J6Fo4a7lDr5fATINHPHadTs3XYU7SJQ7_W2jKQ.lpO61Kw8T2MLlefyXFeuP0wkQ0ayyHvxsYzC2.Ysh0OkiUqt8SS4; path=/; expires=Thu, 05-Jun-25 07:54:16 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/rest/v1/profiles?select=*"}, "revalidate": 31536000, "tags": []}