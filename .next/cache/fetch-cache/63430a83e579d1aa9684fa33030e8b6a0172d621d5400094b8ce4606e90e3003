{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94adecb87a529c44-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/profiles?id=eq.71da82ac-2dcc-411f-b684-bf20a512cddf&select=full_name%2Cemail", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Thu, 05 Jun 2025 07:23:27 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=G1Yv8OwXrX2p7_p1lZJLuWH8c5Mxj7i1cX753dpcDxg-1749108207-*******-375GGQP..JFAGhLgUq_.GUB0XasbsdzNfTnhvHpSPJAxNmDEUrnLgTELLzYzkqWmU3m94BO6vOrHMwxxVoRZb0_v.EFqwa1uZn0BhrDwRZ0; path=/; expires=Thu, 05-Jun-25 07:53:27 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "****************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/rest/v1/profiles?select=full_name%2Cemail&id=eq.71da82ac-2dcc-411f-b684-bf20a512cddf"}, "revalidate": 31536000, "tags": []}