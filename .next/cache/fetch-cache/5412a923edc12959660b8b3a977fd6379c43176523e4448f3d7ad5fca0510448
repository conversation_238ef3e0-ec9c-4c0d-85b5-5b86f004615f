{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94adecb7f99d9c44-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/couples?or=%28user_id_1.eq.3dccb116-689f-4d35-86ee-096970fe9480%2Cuser_id_2.eq.3dccb116-689f-4d35-86ee-096970fe9480%29&select=couple_id%2Cuser_id_1%2Cuser_id_2%2Ccreated_at", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Thu, 05 Jun 2025 07:23:27 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=46.****************************************-1749108207-*******-LesiWqZhj24l3hg.1xQwzfWl3kOVxvRQKCRyheAogVxtrWxkwwq4f.CbOL0.QhDl5JYhD.K_Yew__FMqyvFYr1liaSNnnMgeOrjA7eD.pyc; path=/; expires=Thu, 05-Jun-25 07:53:27 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "5"}, "body": "eyJjb3VwbGVfaWQiOiIxNzNmNzI5Yi04ZTYyLTQ4ODItYWQ0Yi1hODdmOTkwOWI0YmIiLCJ1c2VyX2lkXzEiOiIzZGNjYjExNi02ODlmLTRkMzUtODZlZS0wOTY5NzBmZTk0ODAiLCJ1c2VyX2lkXzIiOiI3MWRhODJhYy0yZGNjLTQxMWYtYjY4NC1iZjIwYTUxMmNkZGYiLCJjcmVhdGVkX2F0IjoiMjAyNS0wNi0wM1QwODoyNjoxMy42NTIyMTMrMDA6MDAifQ==", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/rest/v1/couples?select=couple_id%2Cuser_id_1%2Cuser_id_2%2Ccreated_at&or=%28user_id_1.eq.3dccb116-689f-4d35-86ee-096970fe9480%2Cuser_id_2.eq.3dccb116-689f-4d35-86ee-096970fe9480%29"}, "revalidate": 31536000, "tags": []}