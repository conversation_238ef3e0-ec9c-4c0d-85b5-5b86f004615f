"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/specificity";
exports.ids = ["vendor-chunks/specificity"];
exports.modules = {

/***/ "(ssr)/./node_modules/specificity/dist/specificity.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/specificity/dist/specificity.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculate: () => (/* binding */ calculate),\n/* harmony export */   compare: () => (/* binding */ compare)\n/* harmony export */ });\n// Calculate the specificity for a selector by dividing it into simple selectors and counting them\nvar calculate = function(input) {\n\tvar selectors,\n\t\tselector,\n\t\ti,\n\t\tlen,\n\t\tresults = [];\n\n\t// Separate input by commas\n\tselectors = input.split(',');\n\n\tfor (i = 0, len = selectors.length; i < len; i += 1) {\n\t\tselector = selectors[i];\n\t\tif (selector.length > 0) {\n\t\t\tresults.push(calculateSingle(selector));\n\t\t}\n\t}\n\n\treturn results;\n};\n\n/**\n * Calculates the specificity of CSS selectors\n * http://www.w3.org/TR/css3-selectors/#specificity\n *\n * Returns an object with the following properties:\n *  - selector: the input\n *  - specificity: e.g. 0,1,0,0\n *  - parts: array with details about each part of the selector that counts towards the specificity\n *  - specificityArray: e.g. [0, 1, 0, 0]\n */\nvar calculateSingle = function(input) {\n\tvar selector = input,\n\t\tfindMatch,\n\t\ttypeCount = {\n\t\t\t'a': 0,\n\t\t\t'b': 0,\n\t\t\t'c': 0\n\t\t},\n\t\tparts = [],\n\t\t// The following regular expressions assume that selectors matching the preceding regular expressions have been removed\n\t\tattributeRegex = /(\\[[^\\]]+\\])/g,\n\t\tidRegex = /(#[^\\#\\s\\+>~\\.\\[:\\)]+)/g,\n\t\tclassRegex = /(\\.[^\\s\\+>~\\.\\[:\\)]+)/g,\n\t\tpseudoElementRegex = /(::[^\\s\\+>~\\.\\[:]+|:first-line|:first-letter|:before|:after)/gi,\n\t\t// A regex for pseudo classes with brackets - :nth-child(), :nth-last-child(), :nth-of-type(), :nth-last-type(), :lang()\n\t\t// The negation psuedo class (:not) is filtered out because specificity is calculated on its argument\n\t\t// :global and :local are filtered out - they look like psuedo classes but are an identifier for CSS Modules\n\t\tpseudoClassWithBracketsRegex = /(:(?!not|global|local)[\\w-]+\\([^\\)]*\\))/gi,\n\t\t// A regex for other pseudo classes, which don't have brackets\n\t\tpseudoClassRegex = /(:(?!not|global|local)[^\\s\\+>~\\.\\[:]+)/g,\n\t\telementRegex = /([^\\s\\+>~\\.\\[:]+)/g;\n\n\t// Find matches for a regular expression in a string and push their details to parts\n\t// Type is \"a\" for IDs, \"b\" for classes, attributes and pseudo-classes and \"c\" for elements and pseudo-elements\n\tfindMatch = function(regex, type) {\n\t\tvar matches, i, len, match, index, length;\n\t\tif (regex.test(selector)) {\n\t\t\tmatches = selector.match(regex);\n\t\t\tfor (i = 0, len = matches.length; i < len; i += 1) {\n\t\t\t\ttypeCount[type] += 1;\n\t\t\t\tmatch = matches[i];\n\t\t\t\tindex = selector.indexOf(match);\n\t\t\t\tlength = match.length;\n\t\t\t\tparts.push({\n\t\t\t\t\tselector: input.substr(index, length),\n\t\t\t\t\ttype: type,\n\t\t\t\t\tindex: index,\n\t\t\t\t\tlength: length\n\t\t\t\t});\n\t\t\t\t// Replace this simple selector with whitespace so it won't be counted in further simple selectors\n\t\t\t\tselector = selector.replace(match, Array(length + 1).join(' '));\n\t\t\t}\n\t\t}\n\t};\n\n\t// Replace escaped characters with plain text, using the \"A\" character\n\t// https://www.w3.org/TR/CSS21/syndata.html#characters\n\t(function() {\n\t\tvar replaceWithPlainText = function(regex) {\n\t\t\t\tvar matches, i, len, match;\n\t\t\t\tif (regex.test(selector)) {\n\t\t\t\t\tmatches = selector.match(regex);\n\t\t\t\t\tfor (i = 0, len = matches.length; i < len; i += 1) {\n\t\t\t\t\t\tmatch = matches[i];\n\t\t\t\t\t\tselector = selector.replace(match, Array(match.length + 1).join('A'));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// Matches a backslash followed by six hexadecimal digits followed by an optional single whitespace character\n\t\t\tescapeHexadecimalRegex = /\\\\[0-9A-Fa-f]{6}\\s?/g,\n\t\t\t// Matches a backslash followed by fewer than six hexadecimal digits followed by a mandatory single whitespace character\n\t\t\tescapeHexadecimalRegex2 = /\\\\[0-9A-Fa-f]{1,5}\\s/g,\n\t\t\t// Matches a backslash followed by any character\n\t\t\tescapeSpecialCharacter = /\\\\./g;\n\n\t\treplaceWithPlainText(escapeHexadecimalRegex);\n\t\treplaceWithPlainText(escapeHexadecimalRegex2);\n\t\treplaceWithPlainText(escapeSpecialCharacter);\n\t}());\n\n\t// Remove anything after a left brace in case a user has pasted in a rule, not just a selector\n\t(function() {\n\t\tvar regex = /{[^]*/gm,\n\t\t\tmatches, i, len, match;\n\t\tif (regex.test(selector)) {\n\t\t\tmatches = selector.match(regex);\n\t\t\tfor (i = 0, len = matches.length; i < len; i += 1) {\n\t\t\t\tmatch = matches[i];\n\t\t\t\tselector = selector.replace(match, Array(match.length + 1).join(' '));\n\t\t\t}\n\t\t}\n\t}());\n\n\t// Add attribute selectors to parts collection (type b)\n\tfindMatch(attributeRegex, 'b');\n\n\t// Add ID selectors to parts collection (type a)\n\tfindMatch(idRegex, 'a');\n\n\t// Add class selectors to parts collection (type b)\n\tfindMatch(classRegex, 'b');\n\n\t// Add pseudo-element selectors to parts collection (type c)\n\tfindMatch(pseudoElementRegex, 'c');\n\n\t// Add pseudo-class selectors to parts collection (type b)\n\tfindMatch(pseudoClassWithBracketsRegex, 'b');\n\tfindMatch(pseudoClassRegex, 'b');\n\n\t// Remove universal selector and separator characters\n\tselector = selector.replace(/[\\*\\s\\+>~]/g, ' ');\n\n\t// Remove any stray dots or hashes which aren't attached to words\n\t// These may be present if the user is live-editing this selector\n\tselector = selector.replace(/[#\\.]/g, ' ');\n\n\t// Remove the negation psuedo-class (:not) but leave its argument because specificity is calculated on its argument\n \t// Remove non-standard :local and :global CSS Module identifiers because they do not effect the specificity\n\tselector = selector.replace(/:not/g, '    ');\n\tselector = selector.replace(/:local/g, '      ');\n\tselector = selector.replace(/:global/g, '       ');\n\tselector = selector.replace(/[\\(\\)]/g, ' ');\n\n\t// The only things left should be element selectors (type c)\n\tfindMatch(elementRegex, 'c');\n\n\t// Order the parts in the order they appear in the original selector\n\t// This is neater for external apps to deal with\n\tparts.sort(function(a, b) {\n\t\treturn a.index - b.index;\n\t});\n\n\treturn {\n\t\tselector: input,\n\t\tspecificity: '0,' + typeCount.a.toString() + ',' + typeCount.b.toString() + ',' + typeCount.c.toString(),\n\t\tspecificityArray: [0, typeCount.a, typeCount.b, typeCount.c],\n\t\tparts: parts\n\t};\n};\n\n/**\n * Compares two CSS selectors for specificity\n * Alternatively you can replace one of the CSS selectors with a specificity array\n *\n *  - it returns -1 if a has a lower specificity than b\n *  - it returns 1 if a has a higher specificity than b\n *  - it returns 0 if a has the same specificity than b\n */\nvar compare = function(a, b) {\n\tvar aSpecificity,\n\t\tbSpecificity,\n\t\ti;\n\n\tif (typeof a ==='string') {\n\t\tif (a.indexOf(',') !== -1) {\n\t\t\tthrow 'Invalid CSS selector';\n\t\t} else {\n\t\t\taSpecificity = calculateSingle(a)['specificityArray'];\n\t\t}\n\t} else if (Array.isArray(a)) {\n\t\tif (a.filter(function(e) { return (typeof e === 'number'); }).length !== 4) {\n\t\t\tthrow 'Invalid specificity array';\n\t\t} else {\n\t\t\taSpecificity = a;\n\t\t}\n\t} else {\n\t\tthrow 'Invalid CSS selector or specificity array';\n\t}\n\n\tif (typeof b ==='string') {\n\t\tif (b.indexOf(',') !== -1) {\n\t\t\tthrow 'Invalid CSS selector';\n\t\t} else {\n\t\t\tbSpecificity = calculateSingle(b)['specificityArray'];\n\t\t}\n\t} else if (Array.isArray(b)) {\n\t\tif (b.filter(function(e) { return (typeof e === 'number'); }).length !== 4) {\n\t\t\tthrow 'Invalid specificity array';\n\t\t} else {\n\t\t\tbSpecificity = b;\n\t\t}\n\t} else {\n\t\tthrow 'Invalid CSS selector or specificity array';\n\t}\n\n\tfor (i = 0; i < 4; i += 1) {\n\t\tif (aSpecificity[i] < bSpecificity[i]) {\n\t\t\treturn -1;\n\t\t} else if (aSpecificity[i] > bSpecificity[i]) {\n\t\t\treturn 1;\n\t\t}\n\t}\n\n\treturn 0;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/specificity/dist/specificity.mjs\n");

/***/ })

};
;