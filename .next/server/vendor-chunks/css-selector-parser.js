"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-selector-parser";
exports.ids = ["vendor-chunks/css-selector-parser"];
exports.modules = {

/***/ "(ssr)/./node_modules/css-selector-parser/dist/cjs/ast.js":
/*!**********************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/cjs/ast.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ast = void 0;\nfunction astMethods(type) {\n    return function (generatorName, checkerName) {\n        var _a;\n        return (_a = {},\n            _a[generatorName] = function (props) { return (__assign({ type: type }, props)); },\n            _a[checkerName] = function (entity) {\n                return typeof entity === 'object' && entity !== null && entity.type === type;\n            },\n            _a);\n    };\n}\n/**\n * AST structure generators and matchers.\n * For instance, `ast.selector({rules: [...]})` creates AstSelector and `ast.isSelector(...)` checks if\n * AstSelector was specified.\n *\n * @example\n *\n * // Represents CSS selector: ns|div#user-34.user.user-active[role=\"button\"]:lang(en)::before > *\n * const selector = ast.selector({\n *     rules: [\n *         ast.rule({\n *             items: [\n *                 ast.tagName({name: 'div', namespace: ast.namespaceName({name: 'ns'})}),\n *                 ast.id({name: 'user-34'}),\n *                 ast.className({name: 'user'}),\n *                 ast.className({name: 'user-active'}),\n *                 ast.attribute({\n *                     name: 'role',\n *                     operator: '=',\n *                     value: ast.string({value: 'button'})\n *                 }),\n *                 ast.pseudoClass({\n *                     name: 'lang',\n *                     argument: ast.string({value: 'en'})\n *                 }),\n *                 ast.pseudoElement({name: 'before'})\n *             ],\n *             nestedRule: ast.rule({combinator: '>', items: [ast.wildcardTag()]})\n *         })\n *     ]\n * });\n * console.log(ast.isSelector(selector)); // prints true\n * console.log(ast.isRule(selector)); // prints false\n */\nexports.ast = __assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign({}, astMethods('Selector')('selector', 'isSelector')), astMethods('Rule')('rule', 'isRule')), astMethods('TagName')('tagName', 'isTagName')), astMethods('Id')('id', 'isId')), astMethods('ClassName')('className', 'isClassName')), astMethods('WildcardTag')('wildcardTag', 'isWildcardTag')), astMethods('NamespaceName')('namespaceName', 'isNamespaceName')), astMethods('WildcardNamespace')('wildcardNamespace', 'isWildcardNamespace')), astMethods('NoNamespace')('noNamespace', 'isNoNamespace')), astMethods('Attribute')('attribute', 'isAttribute')), astMethods('PseudoClass')('pseudoClass', 'isPseudoClass')), astMethods('PseudoElement')('pseudoElement', 'isPseudoElement')), astMethods('String')('string', 'isString')), astMethods('Formula')('formula', 'isFormula')), astMethods('FormulaOfSelector')('formulaOfSelector', 'isFormulaOfSelector')), astMethods('Substitution')('substitution', 'isSubstitution'));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/cjs/ast.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/cjs/index.js":
/*!************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/cjs/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ast = exports.render = exports.createParser = void 0;\nvar parser_js_1 = __webpack_require__(/*! ./parser.js */ \"(ssr)/./node_modules/css-selector-parser/dist/cjs/parser.js\");\nObject.defineProperty(exports, \"createParser\", ({ enumerable: true, get: function () { return parser_js_1.createParser; } }));\nvar render_js_1 = __webpack_require__(/*! ./render.js */ \"(ssr)/./node_modules/css-selector-parser/dist/cjs/render.js\");\nObject.defineProperty(exports, \"render\", ({ enumerable: true, get: function () { return render_js_1.render; } }));\nvar ast_js_1 = __webpack_require__(/*! ./ast.js */ \"(ssr)/./node_modules/css-selector-parser/dist/cjs/ast.js\");\nObject.defineProperty(exports, \"ast\", ({ enumerable: true, get: function () { return ast_js_1.ast; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L2Nqcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxXQUFXLEdBQUcsY0FBYyxHQUFHLG9CQUFvQjtBQUNuRCxrQkFBa0IsbUJBQU8sQ0FBQyxnRkFBYTtBQUN2QyxnREFBK0MsRUFBRSxxQ0FBcUMsb0NBQW9DLEVBQUM7QUFDM0gsa0JBQWtCLG1CQUFPLENBQUMsZ0ZBQWE7QUFDdkMsMENBQXlDLEVBQUUscUNBQXFDLDhCQUE4QixFQUFDO0FBQy9HLGVBQWUsbUJBQU8sQ0FBQywwRUFBVTtBQUNqQyx1Q0FBc0MsRUFBRSxxQ0FBcUMsd0JBQXdCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L2Nqcy9pbmRleC5qcz83ZWFlIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5hc3QgPSBleHBvcnRzLnJlbmRlciA9IGV4cG9ydHMuY3JlYXRlUGFyc2VyID0gdm9pZCAwO1xudmFyIHBhcnNlcl9qc18xID0gcmVxdWlyZShcIi4vcGFyc2VyLmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiY3JlYXRlUGFyc2VyXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBwYXJzZXJfanNfMS5jcmVhdGVQYXJzZXI7IH0gfSk7XG52YXIgcmVuZGVyX2pzXzEgPSByZXF1aXJlKFwiLi9yZW5kZXIuanNcIik7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJyZW5kZXJcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHJlbmRlcl9qc18xLnJlbmRlcjsgfSB9KTtcbnZhciBhc3RfanNfMSA9IHJlcXVpcmUoXCIuL2FzdC5qc1wiKTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImFzdFwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gYXN0X2pzXzEuYXN0OyB9IH0pO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/cjs/indexes.js":
/*!**************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/cjs/indexes.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createRegularIndex = exports.createMulticharIndex = exports.emptyRegularIndex = exports.emptyMulticharIndex = void 0;\nexports.emptyMulticharIndex = {};\nexports.emptyRegularIndex = {};\nfunction extendIndex(item, index) {\n    var currentIndex = index;\n    for (var pos = 0; pos < item.length; pos++) {\n        var isLast = pos === item.length - 1;\n        var char = item.charAt(pos);\n        var charIndex = currentIndex[char] || (currentIndex[char] = { chars: {} });\n        if (isLast) {\n            charIndex.self = item;\n        }\n        currentIndex = charIndex.chars;\n    }\n}\nfunction createMulticharIndex(items) {\n    if (items.length === 0) {\n        return exports.emptyMulticharIndex;\n    }\n    var index = {};\n    for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {\n        var item = items_1[_i];\n        extendIndex(item, index);\n    }\n    return index;\n}\nexports.createMulticharIndex = createMulticharIndex;\nfunction createRegularIndex(items) {\n    if (items.length === 0) {\n        return exports.emptyRegularIndex;\n    }\n    var result = {};\n    for (var _i = 0, items_2 = items; _i < items_2.length; _i++) {\n        var item = items_2[_i];\n        result[item] = true;\n    }\n    return result;\n}\nexports.createRegularIndex = createRegularIndex;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/cjs/indexes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/cjs/parser.js":
/*!*************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/cjs/parser.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createParser = void 0;\nvar indexes_js_1 = __webpack_require__(/*! ./indexes.js */ \"(ssr)/./node_modules/css-selector-parser/dist/cjs/indexes.js\");\nvar pseudo_signatures_js_1 = __webpack_require__(/*! ./pseudo-signatures.js */ \"(ssr)/./node_modules/css-selector-parser/dist/cjs/pseudo-signatures.js\");\nvar syntax_definitions_js_1 = __webpack_require__(/*! ./syntax-definitions.js */ \"(ssr)/./node_modules/css-selector-parser/dist/cjs/syntax-definitions.js\");\nvar utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/css-selector-parser/dist/cjs/utils.js\");\nvar errorPrefix = \"css-selector-parser parse error: \";\n/**\n * Creates a parse function to be used later to parse CSS selectors.\n */\nfunction createParser(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.syntax, syntax = _a === void 0 ? 'latest' : _a, substitutes = options.substitutes, _b = options.strict, strict = _b === void 0 ? true : _b;\n    var syntaxDefinition = typeof syntax === 'object' ? syntax : syntax_definitions_js_1.cssSyntaxDefinitions[syntax];\n    if (syntaxDefinition.baseSyntax) {\n        syntaxDefinition = (0, syntax_definitions_js_1.extendSyntaxDefinition)(syntax_definitions_js_1.cssSyntaxDefinitions[syntaxDefinition.baseSyntax], syntaxDefinition);\n    }\n    var _c = syntaxDefinition.tag\n        ? [true, Boolean((0, syntax_definitions_js_1.getXmlOptions)(syntaxDefinition.tag).wildcard)]\n        : [false, false], tagNameEnabled = _c[0], tagNameWildcardEnabled = _c[1];\n    var idEnabled = Boolean(syntaxDefinition.ids);\n    var classNamesEnabled = Boolean(syntaxDefinition.classNames);\n    var namespaceEnabled = Boolean(syntaxDefinition.namespace);\n    var namespaceWildcardEnabled = syntaxDefinition.namespace &&\n        (syntaxDefinition.namespace === true || syntaxDefinition.namespace.wildcard === true);\n    if (namespaceEnabled && !tagNameEnabled) {\n        throw new Error(\"\".concat(errorPrefix, \"Namespaces cannot be enabled while tags are disabled.\"));\n    }\n    var substitutesEnabled = Boolean(substitutes);\n    var combinatorsIndex = syntaxDefinition.combinators\n        ? (0, indexes_js_1.createMulticharIndex)(syntaxDefinition.combinators)\n        : indexes_js_1.emptyMulticharIndex;\n    var _d = syntaxDefinition.attributes\n        ? [\n            true,\n            syntaxDefinition.attributes.operators\n                ? (0, indexes_js_1.createMulticharIndex)(syntaxDefinition.attributes.operators)\n                : indexes_js_1.emptyMulticharIndex,\n            syntaxDefinition.attributes.caseSensitivityModifiers\n                ? (0, indexes_js_1.createRegularIndex)(syntaxDefinition.attributes.caseSensitivityModifiers)\n                : indexes_js_1.emptyRegularIndex,\n            syntaxDefinition.attributes.unknownCaseSensitivityModifiers === 'accept'\n        ]\n        : [false, indexes_js_1.emptyMulticharIndex, indexes_js_1.emptyRegularIndex, false], attributesEnabled = _d[0], attributesOperatorsIndex = _d[1], attributesCaseSensitivityModifiers = _d[2], attributesAcceptUnknownCaseSensitivityModifiers = _d[3];\n    var attributesCaseSensitivityModifiersEnabled = attributesAcceptUnknownCaseSensitivityModifiers || Object.keys(attributesCaseSensitivityModifiers).length > 0;\n    var _e = syntaxDefinition.pseudoClasses\n        ? [\n            true,\n            syntaxDefinition.pseudoClasses.definitions\n                ? (0, pseudo_signatures_js_1.calculatePseudoSignatures)(syntaxDefinition.pseudoClasses.definitions)\n                : pseudo_signatures_js_1.emptyPseudoSignatures,\n            syntaxDefinition.pseudoClasses.unknown === 'accept'\n        ]\n        : [false, pseudo_signatures_js_1.emptyPseudoSignatures, false], pseudoClassesEnabled = _e[0], pseudoClassesDefinitions = _e[1], pseudoClassesAcceptUnknown = _e[2];\n    var _f = syntaxDefinition.pseudoElements\n        ? [\n            true,\n            syntaxDefinition.pseudoElements.notation === 'singleColon' ||\n                syntaxDefinition.pseudoElements.notation === 'both',\n            !syntaxDefinition.pseudoElements.notation ||\n                syntaxDefinition.pseudoElements.notation === 'doubleColon' ||\n                syntaxDefinition.pseudoElements.notation === 'both',\n            syntaxDefinition.pseudoElements.definitions\n                ? (0, pseudo_signatures_js_1.calculatePseudoSignatures)(Array.isArray(syntaxDefinition.pseudoElements.definitions)\n                    ? { NoArgument: syntaxDefinition.pseudoElements.definitions }\n                    : syntaxDefinition.pseudoElements.definitions)\n                : pseudo_signatures_js_1.emptyPseudoSignatures,\n            syntaxDefinition.pseudoElements.unknown === 'accept'\n        ]\n        : [false, false, false, pseudo_signatures_js_1.emptyPseudoSignatures, false], pseudoElementsEnabled = _f[0], pseudoElementsSingleColonNotationEnabled = _f[1], pseudoElementsDoubleColonNotationEnabled = _f[2], pseudoElementsDefinitions = _f[3], pseudoElementsAcceptUnknown = _f[4];\n    var str = '';\n    var l = str.length;\n    var pos = 0;\n    var chr = '';\n    var is = function (comparison) { return chr === comparison; };\n    var isTagStart = function () { return is('*') || (0, utils_js_1.isIdentStart)(chr); };\n    var rewind = function (newPos) {\n        pos = newPos;\n        chr = str.charAt(pos);\n    };\n    var next = function () {\n        pos++;\n        chr = str.charAt(pos);\n    };\n    var readAndNext = function () {\n        var current = chr;\n        pos++;\n        chr = str.charAt(pos);\n        return current;\n    };\n    /** @throws ParserError */\n    function fail(errorMessage) {\n        var position = Math.min(l - 1, pos);\n        var error = new Error(\"\".concat(errorPrefix).concat(errorMessage, \" Pos: \").concat(position, \".\"));\n        error.position = position;\n        error.name = 'ParserError';\n        throw error;\n    }\n    function assert(condition, errorMessage) {\n        if (!condition) {\n            return fail(errorMessage);\n        }\n    }\n    var assertNonEof = function () {\n        assert(pos < l, 'Unexpected end of input.');\n    };\n    var isEof = function () { return pos >= l; };\n    var pass = function (character) {\n        assert(pos < l, \"Expected \\\"\".concat(character, \"\\\" but end of input reached.\"));\n        assert(chr === character, \"Expected \\\"\".concat(character, \"\\\" but \\\"\").concat(chr, \"\\\" found.\"));\n        pos++;\n        chr = str.charAt(pos);\n    };\n    function matchMulticharIndex(index) {\n        var match = matchMulticharIndexPos(index, pos);\n        if (match) {\n            pos += match.length;\n            chr = str.charAt(pos);\n            return match;\n        }\n    }\n    function matchMulticharIndexPos(index, subPos) {\n        var char = str.charAt(subPos);\n        var charIndex = index[char];\n        if (charIndex) {\n            var subMatch = matchMulticharIndexPos(charIndex.chars, subPos + 1);\n            if (subMatch) {\n                return subMatch;\n            }\n            if (charIndex.self) {\n                return charIndex.self;\n            }\n        }\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#hex-digit-diagram\n     */\n    function parseHex() {\n        var hex = readAndNext();\n        var count = 1;\n        while ((0, utils_js_1.isHex)(chr) && count < utils_js_1.maxHexLength) {\n            hex += readAndNext();\n            count++;\n        }\n        skipSingleWhitespace();\n        return String.fromCharCode(parseInt(hex, 16));\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#string-token-diagram\n     */\n    function parseString(quote) {\n        var result = '';\n        pass(quote);\n        while (pos < l) {\n            if (is(quote)) {\n                next();\n                return result;\n            }\n            else if (is('\\\\')) {\n                next();\n                if (is(quote)) {\n                    result += quote;\n                    next();\n                }\n                else if (chr === '\\n' || chr === '\\f') {\n                    next();\n                }\n                else if (chr === '\\r') {\n                    next();\n                    if (is('\\n')) {\n                        next();\n                    }\n                }\n                else if ((0, utils_js_1.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += chr;\n                    next();\n                }\n            }\n            else {\n                result += chr;\n                next();\n            }\n        }\n        return result;\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#ident-token-diagram\n     */\n    function parseIdentifier() {\n        if (!(0, utils_js_1.isIdentStart)(chr)) {\n            return null;\n        }\n        var result = '';\n        while (is('-')) {\n            result += chr;\n            next();\n        }\n        if (result === '-' && !(0, utils_js_1.isIdent)(chr) && !is('\\\\')) {\n            fail('Identifiers cannot consist of a single hyphen.');\n        }\n        if (strict && result.length >= 2) {\n            // Checking this only for strict mode since browsers work fine with these identifiers.\n            fail('Identifiers cannot start with two hyphens with strict mode on.');\n        }\n        if (utils_js_1.digitsChars[chr]) {\n            fail('Identifiers cannot start with hyphens followed by digits.');\n        }\n        while (pos < l) {\n            if ((0, utils_js_1.isIdent)(chr)) {\n                result += readAndNext();\n            }\n            else if (is('\\\\')) {\n                next();\n                assertNonEof();\n                if ((0, utils_js_1.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += readAndNext();\n                }\n            }\n            else {\n                break;\n            }\n        }\n        return result;\n    }\n    function parsePseudoClassString() {\n        var result = '';\n        while (pos < l) {\n            if (is(')')) {\n                break;\n            }\n            else if (is('\\\\')) {\n                next();\n                if (isEof() && !strict) {\n                    return (result + '\\\\').trim();\n                }\n                assertNonEof();\n                if ((0, utils_js_1.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += readAndNext();\n                }\n            }\n            else {\n                result += readAndNext();\n            }\n        }\n        return result.trim();\n    }\n    function skipSingleWhitespace() {\n        if (chr === ' ' || chr === '\\t' || chr === '\\f' || chr === '\\n') {\n            next();\n            return;\n        }\n        if (chr === '\\r') {\n            next();\n        }\n        if (chr === '\\n') {\n            next();\n        }\n    }\n    function skipWhitespace() {\n        while (utils_js_1.whitespaceChars[chr]) {\n            next();\n        }\n    }\n    function parseSelector(relative) {\n        if (relative === void 0) { relative = false; }\n        skipWhitespace();\n        var rules = [parseRule(relative)];\n        while (is(',')) {\n            next();\n            skipWhitespace();\n            rules.push(parseRule(relative));\n        }\n        return {\n            type: 'Selector',\n            rules: rules\n        };\n    }\n    function parseAttribute() {\n        pass('[');\n        skipWhitespace();\n        var attr;\n        if (is('|')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            next();\n            var name_1 = parseIdentifier();\n            assert(name_1, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: name_1,\n                namespace: { type: 'NoNamespace' }\n            };\n        }\n        else if (is('*')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            assert(namespaceWildcardEnabled, 'Wildcard namespace is not enabled.');\n            next();\n            pass('|');\n            var name_2 = parseIdentifier();\n            assert(name_2, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: name_2,\n                namespace: { type: 'WildcardNamespace' }\n            };\n        }\n        else {\n            var identifier = parseIdentifier();\n            assert(identifier, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: identifier\n            };\n            if (is('|')) {\n                var savedPos = pos;\n                next();\n                if ((0, utils_js_1.isIdentStart)(chr)) {\n                    assert(namespaceEnabled, 'Namespaces are not enabled.');\n                    var name_3 = parseIdentifier();\n                    assert(name_3, 'Expected attribute name.');\n                    attr = {\n                        type: 'Attribute',\n                        name: name_3,\n                        namespace: { type: 'NamespaceName', name: identifier }\n                    };\n                }\n                else {\n                    rewind(savedPos);\n                }\n            }\n        }\n        assert(attr.name, 'Expected attribute name.');\n        skipWhitespace();\n        if (isEof() && !strict) {\n            return attr;\n        }\n        if (is(']')) {\n            next();\n        }\n        else {\n            attr.operator = matchMulticharIndex(attributesOperatorsIndex);\n            assert(attr.operator, 'Expected a valid attribute selector operator.');\n            skipWhitespace();\n            assertNonEof();\n            if (utils_js_1.quoteChars[chr]) {\n                attr.value = {\n                    type: 'String',\n                    value: parseString(chr)\n                };\n            }\n            else if (substitutesEnabled && is('$')) {\n                next();\n                var name_4 = parseIdentifier();\n                assert(name_4, 'Expected substitute name.');\n                attr.value = {\n                    type: 'Substitution',\n                    name: name_4\n                };\n            }\n            else {\n                var value = parseIdentifier();\n                assert(value, 'Expected attribute value.');\n                attr.value = {\n                    type: 'String',\n                    value: value\n                };\n            }\n            skipWhitespace();\n            if (isEof() && !strict) {\n                return attr;\n            }\n            if (!is(']')) {\n                var caseSensitivityModifier = parseIdentifier();\n                assert(caseSensitivityModifier, 'Expected end of attribute selector.');\n                attr.caseSensitivityModifier = caseSensitivityModifier;\n                assert(attributesCaseSensitivityModifiersEnabled, 'Attribute case sensitivity modifiers are not enabled.');\n                assert(attributesAcceptUnknownCaseSensitivityModifiers ||\n                    attributesCaseSensitivityModifiers[attr.caseSensitivityModifier], 'Unknown attribute case sensitivity modifier.');\n                skipWhitespace();\n                if (isEof() && !strict) {\n                    return attr;\n                }\n            }\n            pass(']');\n        }\n        return attr;\n    }\n    function parseNumber() {\n        var result = '';\n        while (utils_js_1.digitsChars[chr]) {\n            result += readAndNext();\n        }\n        assert(result !== '', 'Formula parse error.');\n        return parseInt(result);\n    }\n    var isNumberStart = function () { return is('-') || is('+') || utils_js_1.digitsChars[chr]; };\n    function parseFormula() {\n        if (is('e') || is('o')) {\n            var ident = parseIdentifier();\n            if (ident === 'even') {\n                skipWhitespace();\n                return [2, 0];\n            }\n            if (ident === 'odd') {\n                skipWhitespace();\n                return [2, 1];\n            }\n        }\n        var firstNumber = null;\n        var firstNumberMultiplier = 1;\n        if (is('-')) {\n            next();\n            firstNumberMultiplier = -1;\n        }\n        if (isNumberStart()) {\n            if (is('+')) {\n                next();\n            }\n            firstNumber = parseNumber();\n            if (!is('\\\\') && !is('n')) {\n                return [0, firstNumber * firstNumberMultiplier];\n            }\n        }\n        if (firstNumber === null) {\n            firstNumber = 1;\n        }\n        firstNumber *= firstNumberMultiplier;\n        var identifier;\n        if (is('\\\\')) {\n            next();\n            if ((0, utils_js_1.isHex)(chr)) {\n                identifier = parseHex();\n            }\n            else {\n                identifier = readAndNext();\n            }\n        }\n        else {\n            identifier = readAndNext();\n        }\n        assert(identifier === 'n', 'Formula parse error: expected \"n\".');\n        skipWhitespace();\n        if (is('+') || is('-')) {\n            var sign = is('+') ? 1 : -1;\n            next();\n            skipWhitespace();\n            return [firstNumber, sign * parseNumber()];\n        }\n        else {\n            return [firstNumber, 0];\n        }\n    }\n    function parsePseudoArgument(pseudoName, type, signature) {\n        var argument;\n        if (is('(')) {\n            next();\n            skipWhitespace();\n            if (substitutesEnabled && is('$')) {\n                next();\n                var name_5 = parseIdentifier();\n                assert(name_5, 'Expected substitute name.');\n                argument = {\n                    type: 'Substitution',\n                    name: name_5\n                };\n            }\n            else if (signature.type === 'String') {\n                argument = {\n                    type: 'String',\n                    value: parsePseudoClassString()\n                };\n                assert(argument.value, \"Expected \".concat(type, \" argument value.\"));\n            }\n            else if (signature.type === 'Selector') {\n                argument = parseSelector(true);\n            }\n            else if (signature.type === 'Formula') {\n                var _a = parseFormula(), a = _a[0], b = _a[1];\n                argument = {\n                    type: 'Formula',\n                    a: a,\n                    b: b\n                };\n                if (signature.ofSelector) {\n                    skipWhitespace();\n                    if (is('o') || is('\\\\')) {\n                        var ident = parseIdentifier();\n                        assert(ident === 'of', 'Formula of selector parse error.');\n                        skipWhitespace();\n                        argument = {\n                            type: 'FormulaOfSelector',\n                            a: a,\n                            b: b,\n                            selector: parseRule()\n                        };\n                    }\n                }\n            }\n            else {\n                return fail(\"Invalid \".concat(type, \" signature.\"));\n            }\n            skipWhitespace();\n            if (isEof() && !strict) {\n                return argument;\n            }\n            pass(')');\n        }\n        else {\n            assert(signature.optional, \"Argument is required for \".concat(type, \" \\\"\").concat(pseudoName, \"\\\".\"));\n        }\n        return argument;\n    }\n    function parseTagName() {\n        if (is('*')) {\n            assert(tagNameWildcardEnabled, 'Wildcard tag name is not enabled.');\n            next();\n            return { type: 'WildcardTag' };\n        }\n        else if ((0, utils_js_1.isIdentStart)(chr)) {\n            assert(tagNameEnabled, 'Tag names are not enabled.');\n            var name_6 = parseIdentifier();\n            assert(name_6, 'Expected tag name.');\n            return {\n                type: 'TagName',\n                name: name_6\n            };\n        }\n        else {\n            return fail('Expected tag name.');\n        }\n    }\n    function parseTagNameWithNamespace() {\n        if (is('*')) {\n            var savedPos = pos;\n            next();\n            if (!is('|')) {\n                rewind(savedPos);\n                return parseTagName();\n            }\n            next();\n            if (!isTagStart()) {\n                rewind(savedPos);\n                return parseTagName();\n            }\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            assert(namespaceWildcardEnabled, 'Wildcard namespace is not enabled.');\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'WildcardNamespace' };\n            return tagName;\n        }\n        else if (is('|')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            next();\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'NoNamespace' };\n            return tagName;\n        }\n        else if ((0, utils_js_1.isIdentStart)(chr)) {\n            var identifier = parseIdentifier();\n            assert(identifier, 'Expected tag name.');\n            if (!is('|')) {\n                assert(tagNameEnabled, 'Tag names are not enabled.');\n                return {\n                    type: 'TagName',\n                    name: identifier\n                };\n            }\n            var savedPos = pos;\n            next();\n            if (!isTagStart()) {\n                rewind(savedPos);\n                return {\n                    type: 'TagName',\n                    name: identifier\n                };\n            }\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'NamespaceName', name: identifier };\n            return tagName;\n        }\n        else {\n            return fail('Expected tag name.');\n        }\n    }\n    function parseRule(relative) {\n        var _a, _b;\n        if (relative === void 0) { relative = false; }\n        var rule = { type: 'Rule', items: [] };\n        if (relative) {\n            var combinator = matchMulticharIndex(combinatorsIndex);\n            if (combinator) {\n                rule.combinator = combinator;\n                skipWhitespace();\n            }\n        }\n        while (pos < l) {\n            if (isTagStart()) {\n                assert(rule.items.length === 0, 'Unexpected tag/namespace start.');\n                rule.items.push(parseTagNameWithNamespace());\n            }\n            else if (is('|')) {\n                var savedPos = pos;\n                next();\n                if (isTagStart()) {\n                    assert(rule.items.length === 0, 'Unexpected tag/namespace start.');\n                    rewind(savedPos);\n                    rule.items.push(parseTagNameWithNamespace());\n                }\n                else {\n                    rewind(savedPos);\n                    break;\n                }\n            }\n            else if (is('.')) {\n                assert(classNamesEnabled, 'Class names are not enabled.');\n                next();\n                var className = parseIdentifier();\n                assert(className, 'Expected class name.');\n                rule.items.push({ type: 'ClassName', name: className });\n            }\n            else if (is('#')) {\n                assert(idEnabled, 'IDs are not enabled.');\n                next();\n                var idName = parseIdentifier();\n                assert(idName, 'Expected ID name.');\n                rule.items.push({ type: 'Id', name: idName });\n            }\n            else if (is('[')) {\n                assert(attributesEnabled, 'Attributes are not enabled.');\n                rule.items.push(parseAttribute());\n            }\n            else if (is(':')) {\n                var isDoubleColon = false;\n                var isPseudoElement = false;\n                next();\n                if (is(':')) {\n                    assert(pseudoElementsEnabled, 'Pseudo elements are not enabled.');\n                    assert(pseudoElementsDoubleColonNotationEnabled, 'Pseudo elements double colon notation is not enabled.');\n                    isDoubleColon = true;\n                    next();\n                }\n                var pseudoName = parseIdentifier();\n                assert(isDoubleColon || pseudoName, 'Expected pseudo-class name.');\n                assert(!isDoubleColon || pseudoName, 'Expected pseudo-element name.');\n                assert(pseudoName, 'Expected pseudo-class name.');\n                assert(!isDoubleColon ||\n                    pseudoElementsAcceptUnknown ||\n                    Object.prototype.hasOwnProperty.call(pseudoElementsDefinitions, pseudoName), \"Unknown pseudo-element \\\"\".concat(pseudoName, \"\\\".\"));\n                isPseudoElement =\n                    pseudoElementsEnabled &&\n                        (isDoubleColon ||\n                            (!isDoubleColon &&\n                                pseudoElementsSingleColonNotationEnabled &&\n                                Object.prototype.hasOwnProperty.call(pseudoElementsDefinitions, pseudoName)));\n                if (isPseudoElement) {\n                    var signature = (_a = pseudoElementsDefinitions[pseudoName]) !== null && _a !== void 0 ? _a : (pseudoElementsAcceptUnknown && pseudo_signatures_js_1.defaultPseudoSignature);\n                    var pseudoElement = {\n                        type: 'PseudoElement',\n                        name: pseudoName\n                    };\n                    var argument = parsePseudoArgument(pseudoName, 'pseudo-element', signature);\n                    if (argument) {\n                        assert(argument.type !== 'Formula' && argument.type !== 'FormulaOfSelector', 'Pseudo-elements cannot have formula argument.');\n                        pseudoElement.argument = argument;\n                    }\n                    rule.items.push(pseudoElement);\n                }\n                else {\n                    assert(pseudoClassesEnabled, 'Pseudo-classes are not enabled.');\n                    var signature = (_b = pseudoClassesDefinitions[pseudoName]) !== null && _b !== void 0 ? _b : (pseudoClassesAcceptUnknown && pseudo_signatures_js_1.defaultPseudoSignature);\n                    assert(signature, \"Unknown pseudo-class: \\\"\".concat(pseudoName, \"\\\".\"));\n                    var argument = parsePseudoArgument(pseudoName, 'pseudo-class', signature);\n                    var pseudoClass = {\n                        type: 'PseudoClass',\n                        name: pseudoName\n                    };\n                    if (argument) {\n                        pseudoClass.argument = argument;\n                    }\n                    rule.items.push(pseudoClass);\n                }\n            }\n            else {\n                break;\n            }\n        }\n        if (rule.items.length === 0) {\n            if (isEof()) {\n                return fail('Expected rule but end of input reached.');\n            }\n            else {\n                return fail(\"Expected rule but \\\"\".concat(chr, \"\\\" found.\"));\n            }\n        }\n        skipWhitespace();\n        if (!isEof() && !is(',') && !is(')')) {\n            var combinator = matchMulticharIndex(combinatorsIndex);\n            skipWhitespace();\n            rule.nestedRule = parseRule();\n            rule.nestedRule.combinator = combinator;\n        }\n        return rule;\n    }\n    return function (input) {\n        // noinspection SuspiciousTypeOfGuard\n        if (typeof input !== 'string') {\n            throw new Error(\"\".concat(errorPrefix, \"Expected string input.\"));\n        }\n        str = input;\n        l = str.length;\n        pos = 0;\n        chr = str.charAt(0);\n        return parseSelector();\n    };\n}\nexports.createParser = createParser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/cjs/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/cjs/pseudo-signatures.js":
/*!************************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/cjs/pseudo-signatures.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.calculatePseudoSignatures = exports.inverseCategories = exports.defaultPseudoSignature = exports.emptyPseudoSignatures = void 0;\nexports.emptyPseudoSignatures = {};\nexports.defaultPseudoSignature = {\n    type: 'String',\n    optional: true\n};\nfunction calculatePseudoSignature(types) {\n    var result = {\n        type: 'NoArgument',\n        optional: false\n    };\n    function setResultType(type) {\n        if (result.type && result.type !== type && result.type !== 'NoArgument') {\n            throw new Error(\"Conflicting pseudo-class argument type: \\\"\".concat(result.type, \"\\\" vs \\\"\").concat(type, \"\\\".\"));\n        }\n        result.type = type;\n    }\n    for (var _i = 0, types_1 = types; _i < types_1.length; _i++) {\n        var type = types_1[_i];\n        if (type === 'NoArgument') {\n            result.optional = true;\n        }\n        if (type === 'Formula') {\n            setResultType('Formula');\n        }\n        if (type === 'FormulaOfSelector') {\n            setResultType('Formula');\n            result.ofSelector = true;\n        }\n        if (type === 'String') {\n            setResultType('String');\n        }\n        if (type === 'Selector') {\n            setResultType('Selector');\n        }\n    }\n    return result;\n}\nfunction inverseCategories(obj) {\n    var result = {};\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\n        var category = _a[_i];\n        var items = obj[category];\n        if (items) {\n            for (var _b = 0, _c = items; _b < _c.length; _b++) {\n                var item = _c[_b];\n                (result[item] || (result[item] = [])).push(category);\n            }\n        }\n    }\n    return result;\n}\nexports.inverseCategories = inverseCategories;\nfunction calculatePseudoSignatures(definitions) {\n    var pseudoClassesToArgumentTypes = inverseCategories(definitions);\n    var result = {};\n    for (var _i = 0, _a = Object.keys(pseudoClassesToArgumentTypes); _i < _a.length; _i++) {\n        var pseudoClass = _a[_i];\n        var argumentTypes = pseudoClassesToArgumentTypes[pseudoClass];\n        if (argumentTypes) {\n            result[pseudoClass] = calculatePseudoSignature(argumentTypes);\n        }\n    }\n    return result;\n}\nexports.calculatePseudoSignatures = calculatePseudoSignatures;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L2Nqcy9wc2V1ZG8tc2lnbmF0dXJlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQ0FBaUMsR0FBRyx5QkFBeUIsR0FBRyw4QkFBOEIsR0FBRyw2QkFBNkI7QUFDOUgsNkJBQTZCO0FBQzdCLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLHFCQUFxQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxnQkFBZ0I7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLGdCQUFnQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxxRUFBcUUsZ0JBQWdCO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L2Nqcy9wc2V1ZG8tc2lnbmF0dXJlcy5qcz80MmJjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jYWxjdWxhdGVQc2V1ZG9TaWduYXR1cmVzID0gZXhwb3J0cy5pbnZlcnNlQ2F0ZWdvcmllcyA9IGV4cG9ydHMuZGVmYXVsdFBzZXVkb1NpZ25hdHVyZSA9IGV4cG9ydHMuZW1wdHlQc2V1ZG9TaWduYXR1cmVzID0gdm9pZCAwO1xuZXhwb3J0cy5lbXB0eVBzZXVkb1NpZ25hdHVyZXMgPSB7fTtcbmV4cG9ydHMuZGVmYXVsdFBzZXVkb1NpZ25hdHVyZSA9IHtcbiAgICB0eXBlOiAnU3RyaW5nJyxcbiAgICBvcHRpb25hbDogdHJ1ZVxufTtcbmZ1bmN0aW9uIGNhbGN1bGF0ZVBzZXVkb1NpZ25hdHVyZSh0eXBlcykge1xuICAgIHZhciByZXN1bHQgPSB7XG4gICAgICAgIHR5cGU6ICdOb0FyZ3VtZW50JyxcbiAgICAgICAgb3B0aW9uYWw6IGZhbHNlXG4gICAgfTtcbiAgICBmdW5jdGlvbiBzZXRSZXN1bHRUeXBlKHR5cGUpIHtcbiAgICAgICAgaWYgKHJlc3VsdC50eXBlICYmIHJlc3VsdC50eXBlICE9PSB0eXBlICYmIHJlc3VsdC50eXBlICE9PSAnTm9Bcmd1bWVudCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkNvbmZsaWN0aW5nIHBzZXVkby1jbGFzcyBhcmd1bWVudCB0eXBlOiBcXFwiXCIuY29uY2F0KHJlc3VsdC50eXBlLCBcIlxcXCIgdnMgXFxcIlwiKS5jb25jYXQodHlwZSwgXCJcXFwiLlwiKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmVzdWx0LnR5cGUgPSB0eXBlO1xuICAgIH1cbiAgICBmb3IgKHZhciBfaSA9IDAsIHR5cGVzXzEgPSB0eXBlczsgX2kgPCB0eXBlc18xLmxlbmd0aDsgX2krKykge1xuICAgICAgICB2YXIgdHlwZSA9IHR5cGVzXzFbX2ldO1xuICAgICAgICBpZiAodHlwZSA9PT0gJ05vQXJndW1lbnQnKSB7XG4gICAgICAgICAgICByZXN1bHQub3B0aW9uYWwgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlID09PSAnRm9ybXVsYScpIHtcbiAgICAgICAgICAgIHNldFJlc3VsdFR5cGUoJ0Zvcm11bGEnKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZSA9PT0gJ0Zvcm11bGFPZlNlbGVjdG9yJykge1xuICAgICAgICAgICAgc2V0UmVzdWx0VHlwZSgnRm9ybXVsYScpO1xuICAgICAgICAgICAgcmVzdWx0Lm9mU2VsZWN0b3IgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlID09PSAnU3RyaW5nJykge1xuICAgICAgICAgICAgc2V0UmVzdWx0VHlwZSgnU3RyaW5nJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGUgPT09ICdTZWxlY3RvcicpIHtcbiAgICAgICAgICAgIHNldFJlc3VsdFR5cGUoJ1NlbGVjdG9yJyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIGludmVyc2VDYXRlZ29yaWVzKG9iaikge1xuICAgIHZhciByZXN1bHQgPSB7fTtcbiAgICBmb3IgKHZhciBfaSA9IDAsIF9hID0gT2JqZWN0LmtleXMob2JqKTsgX2kgPCBfYS5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgdmFyIGNhdGVnb3J5ID0gX2FbX2ldO1xuICAgICAgICB2YXIgaXRlbXMgPSBvYmpbY2F0ZWdvcnldO1xuICAgICAgICBpZiAoaXRlbXMpIHtcbiAgICAgICAgICAgIGZvciAodmFyIF9iID0gMCwgX2MgPSBpdGVtczsgX2IgPCBfYy5sZW5ndGg7IF9iKyspIHtcbiAgICAgICAgICAgICAgICB2YXIgaXRlbSA9IF9jW19iXTtcbiAgICAgICAgICAgICAgICAocmVzdWx0W2l0ZW1dIHx8IChyZXN1bHRbaXRlbV0gPSBbXSkpLnB1c2goY2F0ZWdvcnkpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59XG5leHBvcnRzLmludmVyc2VDYXRlZ29yaWVzID0gaW52ZXJzZUNhdGVnb3JpZXM7XG5mdW5jdGlvbiBjYWxjdWxhdGVQc2V1ZG9TaWduYXR1cmVzKGRlZmluaXRpb25zKSB7XG4gICAgdmFyIHBzZXVkb0NsYXNzZXNUb0FyZ3VtZW50VHlwZXMgPSBpbnZlcnNlQ2F0ZWdvcmllcyhkZWZpbml0aW9ucyk7XG4gICAgdmFyIHJlc3VsdCA9IHt9O1xuICAgIGZvciAodmFyIF9pID0gMCwgX2EgPSBPYmplY3Qua2V5cyhwc2V1ZG9DbGFzc2VzVG9Bcmd1bWVudFR5cGVzKTsgX2kgPCBfYS5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgdmFyIHBzZXVkb0NsYXNzID0gX2FbX2ldO1xuICAgICAgICB2YXIgYXJndW1lbnRUeXBlcyA9IHBzZXVkb0NsYXNzZXNUb0FyZ3VtZW50VHlwZXNbcHNldWRvQ2xhc3NdO1xuICAgICAgICBpZiAoYXJndW1lbnRUeXBlcykge1xuICAgICAgICAgICAgcmVzdWx0W3BzZXVkb0NsYXNzXSA9IGNhbGN1bGF0ZVBzZXVkb1NpZ25hdHVyZShhcmd1bWVudFR5cGVzKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufVxuZXhwb3J0cy5jYWxjdWxhdGVQc2V1ZG9TaWduYXR1cmVzID0gY2FsY3VsYXRlUHNldWRvU2lnbmF0dXJlcztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/cjs/pseudo-signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/cjs/render.js":
/*!*************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/cjs/render.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.render = void 0;\nvar utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/css-selector-parser/dist/cjs/utils.js\");\nvar errorPrefix = \"css-selector-parser render error: \";\nfunction renderNamespace(namespace) {\n    if (namespace.type === 'WildcardNamespace') {\n        return '*|';\n    }\n    else if (namespace.type === 'NamespaceName') {\n        return \"\".concat((0, utils_js_1.escapeIdentifier)(namespace.name), \"|\");\n    }\n    else if (namespace.type === 'NoNamespace') {\n        return '|';\n    }\n    throw new Error(\"\".concat(errorPrefix, \"Unknown namespace type: \").concat(namespace.type, \".\"));\n}\nfunction renderSubstitution(sub) {\n    return \"$\".concat((0, utils_js_1.escapeIdentifier)(sub.name));\n}\nfunction renderFormula(a, b) {\n    if (a) {\n        var result = \"\".concat(a === 1 ? '' : a === -1 ? '-' : a, \"n\");\n        if (b) {\n            result += \"\".concat(b > 0 ? '+' : '').concat(b);\n        }\n        return result;\n    }\n    else {\n        return String(b);\n    }\n}\n/**\n * Renders CSS Selector AST back to a string.\n *\n * @example\n *\n * import {ast, render} from 'css-selector-parser';\n *\n * const selector = ast.selector({\n *     rules: [\n *         ast.rule({\n *             items: [\n *                 ast.tagName({name: 'a'}),\n *                 ast.id({name: 'user-23'}),\n *                 ast.className({name: 'user'}),\n *                 ast.pseudoClass({name: 'visited'}),\n *                 ast.pseudoElement({name: 'before'})\n *             ]\n *         })\n *     ]\n * });\n *\n * console.log(render(selector)); // a#user-23.user:visited::before\n */\nfunction render(entity) {\n    if (entity.type === 'Selector') {\n        return entity.rules.map(render).join(', ');\n    }\n    if (entity.type === 'Rule') {\n        var result = '';\n        var items = entity.items, combinator = entity.combinator, nestedRule = entity.nestedRule;\n        if (combinator) {\n            result += \"\".concat(combinator, \" \");\n        }\n        for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {\n            var item = items_1[_i];\n            result += render(item);\n        }\n        if (nestedRule) {\n            result += \" \".concat(render(nestedRule));\n        }\n        return result;\n    }\n    else if (entity.type === 'TagName' || entity.type === 'WildcardTag') {\n        var result = '';\n        var namespace = entity.namespace;\n        if (namespace) {\n            result += renderNamespace(namespace);\n        }\n        if (entity.type === 'TagName') {\n            result += (0, utils_js_1.escapeIdentifier)(entity.name);\n        }\n        else if (entity.type === 'WildcardTag') {\n            result += '*';\n        }\n        return result;\n    }\n    else if (entity.type === 'Id') {\n        return \"#\".concat((0, utils_js_1.escapeIdentifier)(entity.name));\n    }\n    else if (entity.type === 'ClassName') {\n        return \".\".concat((0, utils_js_1.escapeIdentifier)(entity.name));\n    }\n    else if (entity.type === 'Attribute') {\n        var name_1 = entity.name, namespace = entity.namespace, operator = entity.operator, value = entity.value, caseSensitivityModifier = entity.caseSensitivityModifier;\n        var result = '[';\n        if (namespace) {\n            result += renderNamespace(namespace);\n        }\n        result += (0, utils_js_1.escapeIdentifier)(name_1);\n        if (operator && value) {\n            result += operator;\n            if (value.type === 'String') {\n                result += (0, utils_js_1.escapeString)(value.value);\n            }\n            else if (value.type === 'Substitution') {\n                result += renderSubstitution(value);\n            }\n            else {\n                throw new Error(\"Unknown attribute value type: \".concat(value.type, \".\"));\n            }\n            if (caseSensitivityModifier) {\n                result += \" \".concat((0, utils_js_1.escapeIdentifier)(caseSensitivityModifier));\n            }\n        }\n        result += ']';\n        return result;\n    }\n    else if (entity.type === 'PseudoClass') {\n        var name_2 = entity.name, argument = entity.argument;\n        var result = \":\".concat((0, utils_js_1.escapeIdentifier)(name_2));\n        if (argument) {\n            result += \"(\".concat(argument.type === 'String' ? (0, utils_js_1.escapeIdentifier)(argument.value) : render(argument), \")\");\n        }\n        return result;\n    }\n    else if (entity.type === 'PseudoElement') {\n        var name_3 = entity.name, argument = entity.argument;\n        var result = \"::\".concat((0, utils_js_1.escapeIdentifier)(name_3));\n        if (argument) {\n            result += \"(\".concat(argument.type === 'String' ? (0, utils_js_1.escapeIdentifier)(argument.value) : render(argument), \")\");\n        }\n        return result;\n    }\n    else if (entity.type === 'String') {\n        throw new Error(\"\".concat(errorPrefix, \"String cannot be rendered outside of context.\"));\n    }\n    else if (entity.type === 'Formula') {\n        return renderFormula(entity.a, entity.b);\n    }\n    else if (entity.type === 'FormulaOfSelector') {\n        return renderFormula(entity.a, entity.b) + ' of ' + render(entity.selector);\n    }\n    else if (entity.type === 'Substitution') {\n        return \"$\".concat((0, utils_js_1.escapeIdentifier)(entity.name));\n    }\n    throw new Error(\"Unknown type specified to render method: \".concat(entity.type, \".\"));\n}\nexports.render = render;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/cjs/render.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/cjs/syntax-definitions.js":
/*!*************************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/cjs/syntax-definitions.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.cssSyntaxDefinitions = exports.extendSyntaxDefinition = exports.getXmlOptions = void 0;\nvar emptyXmlOptions = {};\nvar defaultXmlOptions = { wildcard: true };\nfunction getXmlOptions(param) {\n    if (param) {\n        if (typeof param === 'boolean') {\n            return defaultXmlOptions;\n        }\n        else {\n            return param;\n        }\n    }\n    else {\n        return emptyXmlOptions;\n    }\n}\nexports.getXmlOptions = getXmlOptions;\nfunction withMigration(migration, merge) {\n    return function (base, extension) { return merge(migration(base), migration(extension)); };\n}\nfunction withNoNegative(merge) {\n    return function (base, extension) {\n        var result = merge(base, extension);\n        if (!result) {\n            throw new Error(\"Syntax definition cannot be null or undefined.\");\n        }\n        return result;\n    };\n}\nfunction withPositive(positive, merge) {\n    return function (base, extension) {\n        if (extension === true) {\n            return positive;\n        }\n        return merge(base === true ? positive : base, extension);\n    };\n}\nfunction mergeSection(values) {\n    return function (base, extension) {\n        if (!extension || !base) {\n            return extension;\n        }\n        if (typeof extension !== 'object' || extension === null) {\n            throw new Error(\"Unexpected syntax definition extension type: \".concat(extension, \".\"));\n        }\n        var result = __assign({}, base);\n        for (var _i = 0, _a = Object.entries(extension); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            var mergeSchema = values[key];\n            result[key] = mergeSchema(base[key], value);\n        }\n        return result;\n    };\n}\nfunction replaceValueIfSpecified(base, extension) {\n    if (extension !== undefined) {\n        return extension;\n    }\n    return base;\n}\nfunction concatArray(base, extension) {\n    if (!extension) {\n        return base;\n    }\n    if (!base) {\n        return extension;\n    }\n    return base.concat(extension);\n}\nfunction mergeDefinitions(base, extension) {\n    if (!extension) {\n        return base;\n    }\n    if (!base) {\n        return extension;\n    }\n    var result = __assign({}, base);\n    for (var _i = 0, _a = Object.entries(extension); _i < _a.length; _i++) {\n        var _b = _a[_i], key = _b[0], value = _b[1];\n        if (!value) {\n            delete result[key];\n            continue;\n        }\n        var baseValue = base[key];\n        if (!baseValue) {\n            result[key] = value;\n            continue;\n        }\n        result[key] = baseValue.concat(value);\n    }\n    return result;\n}\nexports.extendSyntaxDefinition = withNoNegative(mergeSection({\n    baseSyntax: replaceValueIfSpecified,\n    tag: withPositive(defaultXmlOptions, mergeSection({\n        wildcard: replaceValueIfSpecified\n    })),\n    ids: replaceValueIfSpecified,\n    classNames: replaceValueIfSpecified,\n    namespace: withPositive(defaultXmlOptions, mergeSection({\n        wildcard: replaceValueIfSpecified\n    })),\n    combinators: concatArray,\n    attributes: mergeSection({\n        operators: concatArray,\n        caseSensitivityModifiers: concatArray,\n        unknownCaseSensitivityModifiers: replaceValueIfSpecified\n    }),\n    pseudoClasses: mergeSection({\n        unknown: replaceValueIfSpecified,\n        definitions: mergeDefinitions\n    }),\n    pseudoElements: mergeSection({\n        unknown: replaceValueIfSpecified,\n        notation: replaceValueIfSpecified,\n        definitions: withMigration(function (definitions) { return (Array.isArray(definitions) ? { NoArgument: definitions } : definitions); }, mergeDefinitions)\n    })\n}));\nvar css1SyntaxDefinition = {\n    tag: {},\n    ids: true,\n    classNames: true,\n    combinators: [],\n    pseudoElements: {\n        unknown: 'reject',\n        notation: 'singleColon',\n        definitions: ['first-letter', 'first-line']\n    },\n    pseudoClasses: {\n        unknown: 'reject',\n        definitions: {\n            NoArgument: ['link', 'visited', 'active']\n        }\n    }\n};\nvar css2SyntaxDefinition = (0, exports.extendSyntaxDefinition)(css1SyntaxDefinition, {\n    tag: { wildcard: true },\n    combinators: ['>', '+'],\n    attributes: {\n        unknownCaseSensitivityModifiers: 'reject',\n        operators: ['=', '~=', '|=']\n    },\n    pseudoElements: {\n        definitions: ['before', 'after']\n    },\n    pseudoClasses: {\n        unknown: 'reject',\n        definitions: {\n            NoArgument: ['hover', 'focus', 'first-child'],\n            String: ['lang']\n        }\n    }\n});\nvar selectors3SyntaxDefinition = (0, exports.extendSyntaxDefinition)(css2SyntaxDefinition, {\n    namespace: {\n        wildcard: true\n    },\n    combinators: ['~'],\n    attributes: {\n        operators: ['^=', '$=', '*=']\n    },\n    pseudoElements: {\n        notation: 'both'\n    },\n    pseudoClasses: {\n        definitions: {\n            NoArgument: [\n                'root',\n                'last-child',\n                'first-of-type',\n                'last-of-type',\n                'only-child',\n                'only-of-type',\n                'empty',\n                'target',\n                'enabled',\n                'disabled',\n                'checked',\n                'indeterminate'\n            ],\n            Formula: ['nth-child', 'nth-last-child', 'nth-of-type', 'nth-last-of-type'],\n            Selector: ['not']\n        }\n    }\n});\nvar selectors4SyntaxDefinition = (0, exports.extendSyntaxDefinition)(selectors3SyntaxDefinition, {\n    combinators: ['||'],\n    attributes: {\n        caseSensitivityModifiers: ['i', 'I', 's', 'S']\n    },\n    pseudoClasses: {\n        definitions: {\n            NoArgument: [\n                'any-link',\n                'local-link',\n                'target-within',\n                'scope',\n                'current',\n                'past',\n                'future',\n                'focus-within',\n                'focus-visible',\n                'read-write',\n                'read-only',\n                'placeholder-shown',\n                'default',\n                'valid',\n                'invalid',\n                'in-range',\n                'out-of-range',\n                'required',\n                'optional',\n                'blank',\n                'user-invalid'\n            ],\n            Formula: ['nth-col', 'nth-last-col'],\n            String: ['dir'],\n            FormulaOfSelector: ['nth-child', 'nth-last-child'],\n            Selector: ['current', 'is', 'where', 'has']\n        }\n    }\n});\nvar progressiveSyntaxDefinition = (0, exports.extendSyntaxDefinition)(selectors4SyntaxDefinition, {\n    pseudoElements: {\n        unknown: 'accept'\n    },\n    pseudoClasses: {\n        unknown: 'accept'\n    },\n    attributes: {\n        unknownCaseSensitivityModifiers: 'accept'\n    }\n});\nexports.cssSyntaxDefinitions = {\n    css1: css1SyntaxDefinition,\n    css2: css2SyntaxDefinition,\n    css3: selectors3SyntaxDefinition,\n    'selectors-3': selectors3SyntaxDefinition,\n    'selectors-4': selectors4SyntaxDefinition,\n    latest: selectors4SyntaxDefinition,\n    progressive: progressiveSyntaxDefinition\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/cjs/syntax-definitions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/cjs/utils.js":
/*!************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/cjs/utils.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.escapeString = exports.escapeIdentifier = exports.maxHexLength = exports.digitsChars = exports.quoteChars = exports.whitespaceChars = exports.stringRenderEscapeChars = exports.identEscapeChars = exports.isHex = exports.isIdent = exports.isIdentStart = void 0;\nfunction isIdentStart(c) {\n    return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c === '-' || c === '_' || c === '\\\\' || c >= '\\u00a0';\n}\nexports.isIdentStart = isIdentStart;\nfunction isIdent(c) {\n    return ((c >= 'a' && c <= 'z') ||\n        (c >= 'A' && c <= 'Z') ||\n        (c >= '0' && c <= '9') ||\n        c === '-' ||\n        c === '_' ||\n        c >= '\\u00a0');\n}\nexports.isIdent = isIdent;\nfunction isHex(c) {\n    return (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F') || (c >= '0' && c <= '9');\n}\nexports.isHex = isHex;\nexports.identEscapeChars = {\n    '!': true,\n    '\"': true,\n    '#': true,\n    $: true,\n    '%': true,\n    '&': true,\n    \"'\": true,\n    '(': true,\n    ')': true,\n    '*': true,\n    '+': true,\n    ',': true,\n    '.': true,\n    '/': true,\n    ';': true,\n    '<': true,\n    '=': true,\n    '>': true,\n    '?': true,\n    '@': true,\n    '[': true,\n    '\\\\': true,\n    ']': true,\n    '^': true,\n    '`': true,\n    '{': true,\n    '|': true,\n    '}': true,\n    '~': true\n};\nexports.stringRenderEscapeChars = {\n    '\\n': true,\n    '\\r': true,\n    '\\t': true,\n    '\\f': true,\n    '\\v': true\n};\nexports.whitespaceChars = {\n    ' ': true,\n    '\\t': true,\n    '\\n': true,\n    '\\r': true,\n    '\\f': true\n};\nexports.quoteChars = {\n    '\"': true,\n    \"'\": true\n};\nexports.digitsChars = {\n    0: true,\n    1: true,\n    2: true,\n    3: true,\n    4: true,\n    5: true,\n    6: true,\n    7: true,\n    8: true,\n    9: true\n};\nexports.maxHexLength = 6;\nfunction escapeIdentifier(s) {\n    var len = s.length;\n    var result = '';\n    var i = 0;\n    while (i < len) {\n        var chr = s.charAt(i);\n        if (exports.identEscapeChars[chr] || (chr === '-' && i === 1 && s.charAt(0) === '-')) {\n            result += '\\\\' + chr;\n        }\n        else {\n            if (chr === '-' ||\n                chr === '_' ||\n                (chr >= 'A' && chr <= 'Z') ||\n                (chr >= 'a' && chr <= 'z') ||\n                (chr >= '0' && chr <= '9' && i !== 0 && !(i === 1 && s.charAt(0) === '-'))) {\n                result += chr;\n            }\n            else {\n                var charCode = chr.charCodeAt(0);\n                if ((charCode & 0xf800) === 0xd800) {\n                    var extraCharCode = s.charCodeAt(i++);\n                    if ((charCode & 0xfc00) !== 0xd800 || (extraCharCode & 0xfc00) !== 0xdc00) {\n                        throw Error('UCS-2(decode): illegal sequence');\n                    }\n                    charCode = ((charCode & 0x3ff) << 10) + (extraCharCode & 0x3ff) + 0x10000;\n                }\n                result += '\\\\' + charCode.toString(16) + ' ';\n            }\n        }\n        i++;\n    }\n    return result.trim();\n}\nexports.escapeIdentifier = escapeIdentifier;\nfunction escapeString(s) {\n    var len = s.length;\n    var result = '';\n    var i = 0;\n    while (i < len) {\n        var chr = s.charAt(i);\n        if (chr === '\"') {\n            chr = '\\\\\"';\n        }\n        else if (chr === '\\\\') {\n            chr = '\\\\\\\\';\n        }\n        else if (exports.stringRenderEscapeChars[chr]) {\n            chr = '\\\\' + chr.charCodeAt(0).toString(16) + (i === len - 1 ? '' : ' ');\n        }\n        result += chr;\n        i++;\n    }\n    return \"\\\"\".concat(result, \"\\\"\");\n}\nexports.escapeString = escapeString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L2Nqcy91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0IsR0FBRyx3QkFBd0IsR0FBRyxvQkFBb0IsR0FBRyxtQkFBbUIsR0FBRyxrQkFBa0IsR0FBRyx1QkFBdUIsR0FBRywrQkFBK0IsR0FBRyx3QkFBd0IsR0FBRyxhQUFhLEdBQUcsZUFBZSxHQUFHLG9CQUFvQjtBQUNqUTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2Isd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2Nzcy1zZWxlY3Rvci1wYXJzZXIvZGlzdC9janMvdXRpbHMuanM/OTA0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZXNjYXBlU3RyaW5nID0gZXhwb3J0cy5lc2NhcGVJZGVudGlmaWVyID0gZXhwb3J0cy5tYXhIZXhMZW5ndGggPSBleHBvcnRzLmRpZ2l0c0NoYXJzID0gZXhwb3J0cy5xdW90ZUNoYXJzID0gZXhwb3J0cy53aGl0ZXNwYWNlQ2hhcnMgPSBleHBvcnRzLnN0cmluZ1JlbmRlckVzY2FwZUNoYXJzID0gZXhwb3J0cy5pZGVudEVzY2FwZUNoYXJzID0gZXhwb3J0cy5pc0hleCA9IGV4cG9ydHMuaXNJZGVudCA9IGV4cG9ydHMuaXNJZGVudFN0YXJ0ID0gdm9pZCAwO1xuZnVuY3Rpb24gaXNJZGVudFN0YXJ0KGMpIHtcbiAgICByZXR1cm4gKGMgPj0gJ2EnICYmIGMgPD0gJ3onKSB8fCAoYyA+PSAnQScgJiYgYyA8PSAnWicpIHx8IGMgPT09ICctJyB8fCBjID09PSAnXycgfHwgYyA9PT0gJ1xcXFwnIHx8IGMgPj0gJ1xcdTAwYTAnO1xufVxuZXhwb3J0cy5pc0lkZW50U3RhcnQgPSBpc0lkZW50U3RhcnQ7XG5mdW5jdGlvbiBpc0lkZW50KGMpIHtcbiAgICByZXR1cm4gKChjID49ICdhJyAmJiBjIDw9ICd6JykgfHxcbiAgICAgICAgKGMgPj0gJ0EnICYmIGMgPD0gJ1onKSB8fFxuICAgICAgICAoYyA+PSAnMCcgJiYgYyA8PSAnOScpIHx8XG4gICAgICAgIGMgPT09ICctJyB8fFxuICAgICAgICBjID09PSAnXycgfHxcbiAgICAgICAgYyA+PSAnXFx1MDBhMCcpO1xufVxuZXhwb3J0cy5pc0lkZW50ID0gaXNJZGVudDtcbmZ1bmN0aW9uIGlzSGV4KGMpIHtcbiAgICByZXR1cm4gKGMgPj0gJ2EnICYmIGMgPD0gJ2YnKSB8fCAoYyA+PSAnQScgJiYgYyA8PSAnRicpIHx8IChjID49ICcwJyAmJiBjIDw9ICc5Jyk7XG59XG5leHBvcnRzLmlzSGV4ID0gaXNIZXg7XG5leHBvcnRzLmlkZW50RXNjYXBlQ2hhcnMgPSB7XG4gICAgJyEnOiB0cnVlLFxuICAgICdcIic6IHRydWUsXG4gICAgJyMnOiB0cnVlLFxuICAgICQ6IHRydWUsXG4gICAgJyUnOiB0cnVlLFxuICAgICcmJzogdHJ1ZSxcbiAgICBcIidcIjogdHJ1ZSxcbiAgICAnKCc6IHRydWUsXG4gICAgJyknOiB0cnVlLFxuICAgICcqJzogdHJ1ZSxcbiAgICAnKyc6IHRydWUsXG4gICAgJywnOiB0cnVlLFxuICAgICcuJzogdHJ1ZSxcbiAgICAnLyc6IHRydWUsXG4gICAgJzsnOiB0cnVlLFxuICAgICc8JzogdHJ1ZSxcbiAgICAnPSc6IHRydWUsXG4gICAgJz4nOiB0cnVlLFxuICAgICc/JzogdHJ1ZSxcbiAgICAnQCc6IHRydWUsXG4gICAgJ1snOiB0cnVlLFxuICAgICdcXFxcJzogdHJ1ZSxcbiAgICAnXSc6IHRydWUsXG4gICAgJ14nOiB0cnVlLFxuICAgICdgJzogdHJ1ZSxcbiAgICAneyc6IHRydWUsXG4gICAgJ3wnOiB0cnVlLFxuICAgICd9JzogdHJ1ZSxcbiAgICAnfic6IHRydWVcbn07XG5leHBvcnRzLnN0cmluZ1JlbmRlckVzY2FwZUNoYXJzID0ge1xuICAgICdcXG4nOiB0cnVlLFxuICAgICdcXHInOiB0cnVlLFxuICAgICdcXHQnOiB0cnVlLFxuICAgICdcXGYnOiB0cnVlLFxuICAgICdcXHYnOiB0cnVlXG59O1xuZXhwb3J0cy53aGl0ZXNwYWNlQ2hhcnMgPSB7XG4gICAgJyAnOiB0cnVlLFxuICAgICdcXHQnOiB0cnVlLFxuICAgICdcXG4nOiB0cnVlLFxuICAgICdcXHInOiB0cnVlLFxuICAgICdcXGYnOiB0cnVlXG59O1xuZXhwb3J0cy5xdW90ZUNoYXJzID0ge1xuICAgICdcIic6IHRydWUsXG4gICAgXCInXCI6IHRydWVcbn07XG5leHBvcnRzLmRpZ2l0c0NoYXJzID0ge1xuICAgIDA6IHRydWUsXG4gICAgMTogdHJ1ZSxcbiAgICAyOiB0cnVlLFxuICAgIDM6IHRydWUsXG4gICAgNDogdHJ1ZSxcbiAgICA1OiB0cnVlLFxuICAgIDY6IHRydWUsXG4gICAgNzogdHJ1ZSxcbiAgICA4OiB0cnVlLFxuICAgIDk6IHRydWVcbn07XG5leHBvcnRzLm1heEhleExlbmd0aCA9IDY7XG5mdW5jdGlvbiBlc2NhcGVJZGVudGlmaWVyKHMpIHtcbiAgICB2YXIgbGVuID0gcy5sZW5ndGg7XG4gICAgdmFyIHJlc3VsdCA9ICcnO1xuICAgIHZhciBpID0gMDtcbiAgICB3aGlsZSAoaSA8IGxlbikge1xuICAgICAgICB2YXIgY2hyID0gcy5jaGFyQXQoaSk7XG4gICAgICAgIGlmIChleHBvcnRzLmlkZW50RXNjYXBlQ2hhcnNbY2hyXSB8fCAoY2hyID09PSAnLScgJiYgaSA9PT0gMSAmJiBzLmNoYXJBdCgwKSA9PT0gJy0nKSkge1xuICAgICAgICAgICAgcmVzdWx0ICs9ICdcXFxcJyArIGNocjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlmIChjaHIgPT09ICctJyB8fFxuICAgICAgICAgICAgICAgIGNociA9PT0gJ18nIHx8XG4gICAgICAgICAgICAgICAgKGNociA+PSAnQScgJiYgY2hyIDw9ICdaJykgfHxcbiAgICAgICAgICAgICAgICAoY2hyID49ICdhJyAmJiBjaHIgPD0gJ3onKSB8fFxuICAgICAgICAgICAgICAgIChjaHIgPj0gJzAnICYmIGNociA8PSAnOScgJiYgaSAhPT0gMCAmJiAhKGkgPT09IDEgJiYgcy5jaGFyQXQoMCkgPT09ICctJykpKSB7XG4gICAgICAgICAgICAgICAgcmVzdWx0ICs9IGNocjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHZhciBjaGFyQ29kZSA9IGNoci5jaGFyQ29kZUF0KDApO1xuICAgICAgICAgICAgICAgIGlmICgoY2hhckNvZGUgJiAweGY4MDApID09PSAweGQ4MDApIHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIGV4dHJhQ2hhckNvZGUgPSBzLmNoYXJDb2RlQXQoaSsrKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKChjaGFyQ29kZSAmIDB4ZmMwMCkgIT09IDB4ZDgwMCB8fCAoZXh0cmFDaGFyQ29kZSAmIDB4ZmMwMCkgIT09IDB4ZGMwMCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgRXJyb3IoJ1VDUy0yKGRlY29kZSk6IGlsbGVnYWwgc2VxdWVuY2UnKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjaGFyQ29kZSA9ICgoY2hhckNvZGUgJiAweDNmZikgPDwgMTApICsgKGV4dHJhQ2hhckNvZGUgJiAweDNmZikgKyAweDEwMDAwO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXN1bHQgKz0gJ1xcXFwnICsgY2hhckNvZGUudG9TdHJpbmcoMTYpICsgJyAnO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGkrKztcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdC50cmltKCk7XG59XG5leHBvcnRzLmVzY2FwZUlkZW50aWZpZXIgPSBlc2NhcGVJZGVudGlmaWVyO1xuZnVuY3Rpb24gZXNjYXBlU3RyaW5nKHMpIHtcbiAgICB2YXIgbGVuID0gcy5sZW5ndGg7XG4gICAgdmFyIHJlc3VsdCA9ICcnO1xuICAgIHZhciBpID0gMDtcbiAgICB3aGlsZSAoaSA8IGxlbikge1xuICAgICAgICB2YXIgY2hyID0gcy5jaGFyQXQoaSk7XG4gICAgICAgIGlmIChjaHIgPT09ICdcIicpIHtcbiAgICAgICAgICAgIGNociA9ICdcXFxcXCInO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGNociA9PT0gJ1xcXFwnKSB7XG4gICAgICAgICAgICBjaHIgPSAnXFxcXFxcXFwnO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGV4cG9ydHMuc3RyaW5nUmVuZGVyRXNjYXBlQ2hhcnNbY2hyXSkge1xuICAgICAgICAgICAgY2hyID0gJ1xcXFwnICsgY2hyLmNoYXJDb2RlQXQoMCkudG9TdHJpbmcoMTYpICsgKGkgPT09IGxlbiAtIDEgPyAnJyA6ICcgJyk7XG4gICAgICAgIH1cbiAgICAgICAgcmVzdWx0ICs9IGNocjtcbiAgICAgICAgaSsrO1xuICAgIH1cbiAgICByZXR1cm4gXCJcXFwiXCIuY29uY2F0KHJlc3VsdCwgXCJcXFwiXCIpO1xufVxuZXhwb3J0cy5lc2NhcGVTdHJpbmcgPSBlc2NhcGVTdHJpbmc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/cjs/utils.js\n");

/***/ })

};
;