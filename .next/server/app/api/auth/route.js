"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/route";
exports.ids = ["app/api/auth/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_yoshuavictor_Nextjs_marriage_map_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/route.ts */ \"(rsc)/./src/app/api/auth/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/route\",\n        pathname: \"/api/auth\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Nextjs/marriage-map/src/app/api/auth/route.ts\",\n    nextConfigOutput,\n    userland: _Users_yoshuavictor_Nextjs_marriage_map_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/auth/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\nasync function POST(request) {\n    try {\n        const { email, password, role, fullName } = await request.json();\n        const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createAdminClient)(); // Use admin client\n        console.log(`/api/auth POST: Attempting to create user ${email} with role ${role}`);\n        // Sign up the user using admin privileges\n        const { data: authData, error: authError } = await supabase.auth.admin.createUser({\n            email,\n            password,\n            email_confirm: true\n        });\n        if (authError) {\n            console.error(\"/api/auth POST: Error creating user via supabase.auth.admin.createUser:\", authError.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: authError.message\n            }, {\n                status: 400\n            });\n        }\n        if (!authData.user) {\n            console.error(\"/api/auth POST: Failed to create user, no user data returned from createUser.\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Failed to create user\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(`/api/auth POST: User created successfully via admin client: ${authData.user.id}`);\n        // Create profile based on role\n        if (role === \"admin\") {\n            console.log(`/api/auth POST: Creating admin_users entry for ${authData.user.id}`);\n            const { error: adminInsertError } = await supabase.from(\"admin_users\").insert([\n                {\n                    user_id: authData.user.id\n                }\n            ]);\n            if (adminInsertError) {\n                console.error(\"/api/auth POST: Error creating admin_users entry:\", adminInsertError.message);\n                // Consider cleanup of created auth user if this critical step fails\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    error: adminInsertError.message\n                }, {\n                    status: 400\n                });\n            }\n        } else if (role === \"counselor\") {\n            const displayName = fullName || email.split(\"@\")[0];\n            console.log(`/api/auth POST: Creating counselor_profiles entry for ${authData.user.id}`);\n            const { error: counselorInsertError } = await supabase.from(\"counselor_profiles\").insert([\n                {\n                    user_id: authData.user.id,\n                    full_name: displayName\n                }\n            ]);\n            if (counselorInsertError) {\n                console.error(\"/api/auth POST: Error creating counselor_profiles entry:\", counselorInsertError.message);\n                // Consider cleanup\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    error: counselorInsertError.message\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Create regular profile for all users\n        const defaultDisplayName = fullName || email.split(\"@\")[0];\n        console.log(`/api/auth POST: Upserting profiles entry for ${authData.user.id}`);\n        const { error: profileUpsertError } = await supabase.from(\"profiles\").upsert({\n            id: authData.user.id,\n            full_name: defaultDisplayName,\n            email\n        }, {\n            onConflict: \"id\"\n        } // Assumes 'id' is the column causing conflict for 'profiles_pkey'\n        );\n        if (profileUpsertError) {\n            console.error(\"/api/auth POST: Error upserting profiles entry:\", profileUpsertError.message);\n            // Consider cleanup\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: profileUpsertError.message\n            }, {\n                status: 400\n            });\n        }\n        console.log(`/api/auth POST: Successfully processed registration for ${authData.user.id}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            message: \"User created successfully\",\n            user: authData.user,\n            role: role\n        });\n    } catch (error) {\n        console.error(\"/api/auth POST: Unhandled exception:\", error.message ? error.message : error, error.stack);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Internal server error during registration\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createAdminClient)(); // Use admin client\n        const authHeader = request.headers.get(\"Authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            console.log(\"/api/auth GET: No token provided\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"No token provided\"\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"/api/auth GET: Received token:\", token ? \"Exists\" : \"Empty/Null\");\n        const { data: { user }, error: getUserError } = await supabase.auth.getUser(token); // Pass token\n        console.log(\"/api/auth GET: getUser result - user:\", user ? user.id : null, \"error:\", getUserError ? getUserError.message : null);\n        if (getUserError) {\n            console.error(\"/api/auth GET: Error from supabase.auth.getUser:\", getUserError.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: getUserError.message\n            }, {\n                status: 401\n            });\n        }\n        if (!user) {\n            console.log(\"/api/auth GET: Not authenticated, no user found for token.\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        console.log(`/api/auth GET: Authenticated user ID: ${user.id}`);\n        // Check admin role\n        console.log(`/api/auth GET: Checking admin_users for user_id: ${user.id}`);\n        const { data: adminRecords, error: adminQueryError } = await supabase.from(\"admin_users\").select(\"user_id\") // Only need to check for existence\n        .eq(\"user_id\", user.id).limit(1);\n        if (adminQueryError) {\n            console.error(\"/api/auth GET: Database error querying admin_users:\", adminQueryError.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Server error checking admin role\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"/api/auth GET: admin_users query result - records count:\", adminRecords ? adminRecords.length : \"null\");\n        if (adminRecords && adminRecords.length > 0) {\n            console.log(`/api/auth GET: User ${user.id} is admin.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                user,\n                role: \"admin\"\n            });\n        }\n        // Check counselor role\n        console.log(`/api/auth GET: Checking counselor_profiles for user_id: ${user.id}`);\n        const { data: counselorRecords, error: counselorQueryError } = await supabase.from(\"counselor_profiles\").select(\"user_id\") // Only need to check for existence\n        .eq(\"user_id\", user.id).limit(1);\n        if (counselorQueryError) {\n            console.error(\"/api/auth GET: Database error querying counselor_profiles:\", counselorQueryError.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Server error checking counselor role\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"/api/auth GET: counselor_profiles query result - records count:\", counselorRecords ? counselorRecords.length : \"null\");\n        if (counselorRecords && counselorRecords.length > 0) {\n            console.log(`/api/auth GET: User ${user.id} is counselor.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                user,\n                role: \"counselor\"\n            });\n        }\n        console.log(`/api/auth GET: User ${user.id} is default user.`);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            user,\n            role: \"user\"\n        });\n    } catch (error) {\n        console.error(\"/api/auth GET: Unhandled exception in GET handler:\", error.message ? error.message : error, error.stack);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Internal server error from GET handler\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   getClient: () => (/* binding */ getClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Check if the code is running in a browser environment\nconst isBrowser = \"undefined\" !== \"undefined\";\n// These will store the singleton instances\nlet supabaseClient = null;\nlet supabaseAdminClient = null;\n// Get the Supabase URL and anon key from environment variables\nconst supabaseUrl = \"https://eqghwtejdnzgopmcjlho.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // For admin operations\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\n/**\n * Get or create the Supabase client for client-side usage\n * This should be used in browser environments only\n */ const createClient = ()=>{\n    if (!isBrowser) {\n        throw new Error(\"createClient should only be called on the client side\");\n    }\n    if (!supabaseClient) {\n        supabaseClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n            auth: {\n                persistSession: true,\n                autoRefreshToken: true,\n                detectSessionInUrl: true\n            }\n        });\n    }\n    return supabaseClient;\n};\n/**\n * Get or create the admin Supabase client with service role key\n * This should be used in server-side or API routes only\n */ const createAdminClient = ()=>{\n    if (!supabaseAdminClient) {\n        if (!supabaseServiceKey) {\n            throw new Error(\"SUPABASE_SERVICE_ROLE_KEY is required for admin operations\");\n        }\n        supabaseAdminClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n            auth: {\n                autoRefreshToken: false,\n                persistSession: false\n            }\n        });\n    }\n    return supabaseAdminClient;\n};\n/**\n * Get the current Supabase client instance\n * This is the preferred way to access the client in components\n */ const getClient = ()=>{\n    if (isBrowser) {\n        return createClient();\n    }\n    return createAdminClient();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();