-- Clean and fix all RLS policies
-- Run this in Supabase SQL Editor

-- 1. CLEAN ALL EXISTING POLICIES
-- Drop all policies for couples
DROP POLICY IF EXISTS "Users can view their couples" ON couples;
DROP POLICY IF EXISTS "Users can create couples" ON couples;
DROP POLICY IF EXISTS "Couple members can view their couple record" ON couples;
DROP POLICY IF EXISTS "Users can create couple records" ON couples;

-- Drop all policies for individual_results
DROP POLICY IF EXISTS "Users can view their own results" ON individual_results;
DROP POLICY IF EXISTS "Users can insert their own results" ON individual_results;
DROP POLICY IF EXISTS "Users can update their own results" ON individual_results;
DROP POLICY IF EXISTS "Users can view their own individual results" ON individual_results;

-- Drop all policies for profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view partner profiles" ON profiles;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone." ON profiles;
DROP POLICY IF EXISTS "Users can update own profile." ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile." ON profiles;

-- 2. CREATE SIMPLE, WORKING POLICIES

-- Couples table policies
CREATE POLICY "couples_select_policy"
  ON couples FOR SELECT
  USING (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

CREATE POLICY "couples_insert_policy"
  ON couples FOR INSERT
  WITH CHECK (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

-- Individual results policies
CREATE POLICY "individual_results_select_policy"
  ON individual_results FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "individual_results_insert_policy"
  ON individual_results FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "individual_results_update_policy"
  ON individual_results FOR UPDATE
  USING (auth.uid() = user_id);

-- Profiles policies (simple and permissive)
CREATE POLICY "profiles_select_policy"
  ON profiles FOR SELECT
  USING (true); -- Allow all authenticated users to read profiles

CREATE POLICY "profiles_insert_policy"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_policy"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

-- 3. VERIFY POLICIES
SELECT 
  tablename, 
  policyname, 
  cmd
FROM pg_policies 
WHERE tablename IN ('couples', 'individual_results', 'profiles')
ORDER BY tablename, cmd;
