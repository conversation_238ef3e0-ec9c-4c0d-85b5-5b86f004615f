-- Check and fix couples table RLS policies
-- Run this in Supabase SQL Editor

-- Check current policies for couples table
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual
FROM pg_policies 
WHERE tablename = 'couples';

-- Check if R<PERSON> is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'couples';

-- Test query manually (replace with actual user IDs from your data)
-- This should return the couple record
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at
FROM couples
WHERE user_id_1 = '3dccb116-689f-4d35-86ee-096970fe9480' 
   OR user_id_2 = '3dccb116-689f-4d35-86ee-096970fe9480'
   OR user_id_1 = '71da82ac-2dcc-411f-b684-bf20a512cddf'
   OR user_id_2 = '71da82ac-2dcc-411f-b684-bf20a512cddf';

-- If the above query returns empty, let's recreate the policies
DROP POLICY IF EXISTS "Users can view their couples" ON couples;
CREATE POLICY "Users can view their couples"
  ON couples FOR SELECT
  USING (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

DROP POLICY IF EXISTS "Users can create couples" ON couples;
CREATE POLICY "Users can create couples"
  ON couples FOR INSERT
  WITH CHECK (auth.uid() = user_id_1 OR auth.uid() = user_id_2);
