-- Test user access to couples table
-- Run this in Supabase SQL Editor

-- Test with actual user IDs from your data
SET LOCAL "request.jwt.claims" = '{"sub": "71da82ac-2dcc-411f-b684-bf20a512cddf"}';

-- This should return the couple record for user 71da82ac-2dcc-411f-b684-bf20a512cddf
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at
FROM couples
WHERE user_id_1 = '71da82ac-2dcc-411f-b684-bf20a512cddf' 
   OR user_id_2 = '71da82ac-2dcc-411f-b684-bf20a512cddf';

-- Reset
RESET ALL;

-- Test with the other user
SET LOCAL "request.jwt.claims" = '{"sub": "3dccb116-689f-4d35-86ee-096970fe9480"}';

-- This should return the couple record for user 3dccb116-689f-4d35-86ee-096970fe9480
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at
FROM couples
WHERE user_id_1 = '3dccb116-689f-4d35-86ee-096970fe9480' 
   OR user_id_2 = '3dccb116-689f-4d35-86ee-096970fe9480';

-- Reset
RESET ALL;
