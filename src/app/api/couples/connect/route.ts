import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function POST(request: Request) {
  try {
    const { code } = await request.json();
    
    if (!code || typeof code !== "string" || code.length !== 6) {
      return NextResponse.json(
        { error: "Invalid code format. Code must be 6 characters." },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is already in a couple
    const { data: existingCouple } = await supabase
      .from("couples")
      .select("couple_id")
      .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
      .single();

    if (existingCouple) {
      return NextResponse.json(
        { error: "You are already connected with a partner" },
        { status: 400 }
      );
    }

    // Find the invitation code
    const { data: invitationCode, error: codeError } = await supabase
      .from("couple_invitation_codes")
      .select("*")
      .eq("code", code.toUpperCase())
      .eq("is_active", true)
      .single();

    if (codeError || !invitationCode) {
      return NextResponse.json(
        { error: "Invalid or expired invitation code" },
        { status: 400 }
      );
    }

    // Check if code has expired
    if (new Date(invitationCode.expires_at) < new Date()) {
      return NextResponse.json(
        { error: "Invitation code has expired" },
        { status: 400 }
      );
    }

    // Check if code has already been used
    if (invitationCode.used_by_user_id) {
      return NextResponse.json(
        { error: "Invitation code has already been used" },
        { status: 400 }
      );
    }

    // Check if user is trying to use their own code
    if (invitationCode.creator_user_id === user.id) {
      return NextResponse.json(
        { error: "You cannot use your own invitation code" },
        { status: 400 }
      );
    }

    // Check if creator is already in a couple
    const { data: creatorCouple } = await supabase
      .from("couples")
      .select("couple_id")
      .or(`user_id_1.eq.${invitationCode.creator_user_id},user_id_2.eq.${invitationCode.creator_user_id}`)
      .single();

    if (creatorCouple) {
      return NextResponse.json(
        { error: "The code creator is already connected with someone else" },
        { status: 400 }
      );
    }

    // Start transaction: Create couple and update invitation code
    const { data: couple, error: coupleError } = await supabase
      .from("couples")
      .insert({
        user_id_1: invitationCode.creator_user_id,
        user_id_2: user.id,
      })
      .select()
      .single();

    if (coupleError) {
      console.error("Error creating couple:", coupleError);
      return NextResponse.json(
        { error: "Failed to create couple connection" },
        { status: 500 }
      );
    }

    // Update invitation code as used
    const { error: updateError } = await supabase
      .from("couple_invitation_codes")
      .update({
        used_by_user_id: user.id,
        used_at: new Date().toISOString(),
        is_active: false,
      })
      .eq("id", invitationCode.id);

    if (updateError) {
      console.error("Error updating invitation code:", updateError);
      // Note: We don't return error here as couple is already created
    }

    // Get partner information
    const { data: partnerProfile } = await supabase
      .from("profiles")
      .select("full_name, email")
      .eq("id", invitationCode.creator_user_id)
      .single();

    return NextResponse.json({
      success: true,
      couple_id: couple.couple_id,
      partner: {
        id: invitationCode.creator_user_id,
        name: partnerProfile?.full_name || "Partner",
        email: partnerProfile?.email,
      },
      message: "Successfully connected with your partner!"
    });

  } catch (error) {
    console.error("Error in connect API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
