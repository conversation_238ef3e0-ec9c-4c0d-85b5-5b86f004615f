import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { cookies } from "next/headers";

export async function POST(request: Request) {
    try {
        const cookieStore = cookies();
        const supabase = createClient();

        // Get current user
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser();

        if (userError || !user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 },
            );
        }

        // Check if user is already in a couple
        const { data: existingCouple } = await supabase
            .from("couples")
            .select("couple_id")
            .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
            .single();

        if (existingCouple) {
            return NextResponse.json(
                { error: "You are already connected with a partner" },
                { status: 400 },
            );
        }

        // Deactivate any existing active codes for this user
        await supabase
            .from("couple_invitation_codes")
            .update({ is_active: false })
            .eq("creator_user_id", user.id)
            .eq("is_active", true);

        // Generate a unique 6-character code
        let code: string;
        let isUnique = false;
        let attempts = 0;
        const maxAttempts = 10;

        do {
            code = Math.random().toString(36).substring(2, 8).toUpperCase();

            // Check if code already exists
            const { data: existingCode } = await supabase
                .from("couple_invitation_codes")
                .select("id")
                .eq("code", code)
                .eq("is_active", true)
                .single();

            isUnique = !existingCode;
            attempts++;
        } while (!isUnique && attempts < maxAttempts);

        if (!isUnique) {
            return NextResponse.json(
                { error: "Failed to generate unique code. Please try again." },
                { status: 500 },
            );
        }

        // Create the invitation code
        const { data: invitationCode, error: codeError } = await supabase
            .from("couple_invitation_codes")
            .insert({
                code,
                creator_user_id: user.id,
                expires_at: new Date(
                    Date.now() + 7 * 24 * 60 * 60 * 1000,
                ).toISOString(), // 7 days from now
            })
            .select()
            .single();

        if (codeError) {
            console.error("Error creating invitation code:", codeError);
            return NextResponse.json(
                { error: "Failed to create invitation code" },
                { status: 500 },
            );
        }

        return NextResponse.json({
            code: invitationCode.code,
            expires_at: invitationCode.expires_at,
            message: "Invitation code generated successfully",
        });
    } catch (error) {
        console.error("Error in generate-code API:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
