"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, ArrowLeft, UserCog, Star, Clock, Users } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";

interface CounselorProfile {
  id: string;
  full_name: string;
  email: string;
  specialization?: string;
  experience_years?: number;
  rating?: number;
  assigned_couples_count?: number;
  bio?: string;
  availability?: string;
}

export default function CounselorSelectPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [counselors, setCounselors] = useState<CounselorProfile[]>([]);
  const [selectedCounselor, setSelectedCounselor] = useState<string | null>(null);
  const [assigning, setAssigning] = useState(false);

  useEffect(() => {
    loadCounselors();
  }, []);

  const loadCounselors = async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = createClient();

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        console.error("User authentication error:", userError);
        throw new Error("User not authenticated");
      }

      console.log("Current user:", user.id);

      // Check if user is in a couple
      const { data: couple, error: coupleError } = await supabase
        .from("couples")
        .select("couple_id")
        .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
        .single();

      console.log("Couple query result:", { couple, coupleError });

      if (!couple) {
          throw new Error("You need to be connected with your partner first before selecting a counselor.");
      }

      // First, let's try a simple query without joins to see if we have any counselor profiles
      const { data: counselorProfiles, error: counselorsError } = await supabase
        .from("counselor_profiles")
        .select(`
          id,
          user_id,
          full_name,
          specialization,
          experience_years,
          bio,
          availability
        `)
        .order('full_name');

      console.log("Counselor profiles query result:", { counselorProfiles, counselorsError });

      if (counselorsError) {
        console.error("Counselors query error:", counselorsError);
        throw new Error(`Failed to load counselors: ${counselorsError.message}`);
      }

      // If no counselor profiles exist, show appropriate message
      if (!counselorProfiles || counselorProfiles.length === 0) {
        console.log("No counselor profiles found in database");
        setCounselors([]);
        return;
      }

      // Get assignment counts for each counselor
      const counselorIds = counselorProfiles?.map(c => c.user_id) || [];
      const { data: assignments } = await supabase
        .from("counselor_couple_assignments")
        .select("counselor_id")
        .in("counselor_id", counselorIds);

      // Count assignments per counselor
      const assignmentCounts = assignments?.reduce((acc: Record<string, number>, assignment) => {
        acc[assignment.counselor_id] = (acc[assignment.counselor_id] || 0) + 1;
        return acc;
      }, {}) || {};

      // Get email addresses for counselors from profiles table
      const counselorUserIds = counselorProfiles.map(c => c.user_id);
      const { data: profilesData } = await supabase
        .from("profiles")
        .select("id, email")
        .in("id", counselorUserIds);

      console.log("Profiles data:", profilesData);

      // Create a map of user_id to email
      const emailMap = profilesData?.reduce((acc: Record<string, string>, profile) => {
        acc[profile.id] = profile.email;
        return acc;
      }, {}) || {};

      // Format counselor data
      const formattedCounselors: CounselorProfile[] = counselorProfiles.map(counselor => ({
        id: counselor.user_id, // Use user_id as the identifier for assignments
        full_name: counselor.full_name || 'Unknown Counselor',
        email: emailMap[counselor.user_id] || 'No email',
        specialization: counselor.specialization,
        experience_years: counselor.experience_years,
        bio: counselor.bio,
        availability: counselor.availability,
        assigned_couples_count: assignmentCounts[counselor.user_id] || 0,
        rating: 4.5 + Math.random() * 0.5 // Mock rating for demo
      }));

      console.log("Final formatted counselors:", formattedCounselors);
      setCounselors(formattedCounselors);

    } catch (err) {
      console.error("Error loading counselors:", err);
      setError(err instanceof Error ? err.message : "Failed to load counselors");
    } finally {
      setLoading(false);
    }
  };

  const handleSelectCounselor = async (counselorId: string) => {
    try {
      setAssigning(true);
      setError(null);

      const supabase = createClient();

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("User not authenticated");
      }

      // Get couple information
      const { data: couple, error: coupleQueryError } = await supabase
        .from("couples")
        .select("couple_id")
        .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
        .single();

      console.log("Couple query for assignment:", { couple, coupleQueryError });

      if (!couple) {
        throw new Error("You need to be connected with your partner first before selecting a counselor.");
      }

      // Check if couple already has a counselor assigned
      const { data: existingAssignment, error: assignmentQueryError } = await supabase
        .from("counselor_couple_assignments")
        .select("id, counselor_id")
        .eq("couple_id", couple.couple_id)
        .single();

      console.log("Existing assignment check:", { existingAssignment, assignmentQueryError });

      if (existingAssignment) {
        // Update existing assignment
        const { error: updateError } = await supabase
          .from("counselor_couple_assignments")
          .update({
            counselor_id: counselorId,
            status: 'active',
            updated_at: new Date().toISOString()
          })
          .eq("id", existingAssignment.id);

        console.log("Update assignment result:", { updateError });

        if (updateError) {
          throw new Error(`Failed to update counselor assignment: ${updateError.message}`);
        }
      } else {
        // Create new assignment
        const { data: newAssignment, error: insertError } = await supabase
          .from("counselor_couple_assignments")
          .insert({
            counselor_id: counselorId,
            couple_id: couple.couple_id,
            status: 'active'
          })
          .select()
          .single();

        console.log("Create assignment result:", { newAssignment, insertError });

        if (insertError) {
          throw new Error(`Failed to assign counselor: ${insertError.message}`);
        }
      }

      // Show success message and redirect
      alert("Counselor assigned successfully!");

      // Redirect to couple dashboard with a small delay to ensure data is updated
      setTimeout(() => {
        router.push("/couple/dashboard");
      }, 1000);

    } catch (err) {
      console.error("Error assigning counselor:", err);
      setError(err instanceof Error ? err.message : "Failed to assign counselor");
    } finally {
      setAssigning(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading available counselors...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-8">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => router.push("/couple/dashboard")} variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Select Your Counselor</h1>
            <p className="text-muted-foreground mt-2">
              Choose a counselor who best fits your needs and preferences
            </p>
          </div>
          <Button onClick={() => router.push("/couple/dashboard")} variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {counselors.map((counselor) => (
          <Card 
            key={counselor.id} 
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedCounselor === counselor.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => setSelectedCounselor(counselor.id)}
          >
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 rounded-full p-2">
                  <UserCog className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-lg">{counselor.full_name}</CardTitle>
                  {counselor.specialization && (
                    <Badge variant="secondary" className="mt-1">
                      {counselor.specialization}
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {counselor.rating && (
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{counselor.rating.toFixed(1)}</span>
                  </div>
                )}
                
                {counselor.experience_years && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{counselor.experience_years} years experience</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{counselor.assigned_couples_count || 0} couples assigned</span>
                </div>

                {counselor.bio && (
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {counselor.bio}
                  </p>
                )}

                {counselor.availability && (
                  <div className="text-sm">
                    <span className="font-medium">Available: </span>
                    <span className="text-muted-foreground">{counselor.availability}</span>
                  </div>
                )}
              </div>

              <Button 
                className="w-full mt-4" 
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelectCounselor(counselor.id);
                }}
                disabled={assigning}
                variant={selectedCounselor === counselor.id ? "default" : "outline"}
              >
                {assigning && selectedCounselor === counselor.id ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Assigning...
                  </>
                ) : (
                  "Select This Counselor"
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {counselors.length === 0 && !loading && (
        <div className="text-center py-12">
          <UserCog className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No Counselors Available</h3>
          <p className="text-muted-foreground mb-4">
            There are currently no counselors registered in the system.
          </p>
          <p className="text-sm text-muted-foreground mb-4">
            Counselors need to register through the counselor registration portal first.
          </p>
          <div className="space-y-2">
            <Button
              onClick={() => window.open("/counselor/register", "_blank")}
              variant="outline"
            >
              Counselor Registration
            </Button>
            <Button
              onClick={() => loadCounselors()}
              variant="ghost"
            >
              Refresh List
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
