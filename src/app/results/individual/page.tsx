"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, Download, ArrowLeft } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface IndividualResultsData {
  id: string;
  domains: Array<{
    domain: string;
    score: number;
    subcategories?: Record<string, number>;
    responses: Record<string, any>;
  }>;
  overall_score: number;
  created_at: string;
  updated_at: string;
}

export default function IndividualResultsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<IndividualResultsData | null>(null);

  useEffect(() => {
    loadIndividualResults();
  }, []);

  const loadIndividualResults = async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = createClient();

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        throw new Error("User not authenticated");
      }

      // Get individual results
      const { data: individualResults, error: resultsError } = await supabase
        .from("individual_results")
        .select("*")
        .eq("user_id", user.id)
        .single();

      if (resultsError) {
        if (resultsError.code === "PGRST116") {
          throw new Error("No assessment results found. Please complete your assessments first.");
        }
        throw new Error("Failed to load assessment results");
      }

      setResults(individualResults);

    } catch (err) {
      console.error("Error loading individual results:", err);
      setError(err instanceof Error ? err.message : "Failed to load results");
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreLevel = (score: number) => {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    if (score >= 40) return "Fair";
    return "Needs Improvement";
  };

  const downloadResults = () => {
    if (!results) return;

    const reportText = `
LAPORAN HASIL ASSESSMENT PRIBADI
===============================

Tanggal: ${new Date().toLocaleDateString('id-ID')}
Skor Keseluruhan: ${results.overall_score}% (${getScoreLevel(results.overall_score)})

DETAIL PER DOMAIN:
${results.domains.map(domain => `
${domain.domain}: ${domain.score}%
- Level: ${getScoreLevel(domain.score)}
${domain.subcategories ? Object.entries(domain.subcategories).map(([key, value]) => `- ${key}: ${value}%`).join('\n') : ''}
`).join('\n')}

Laporan dibuat pada: ${new Date().toLocaleString('id-ID')}
    `;

    const blob = new Blob([reportText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hasil-assessment-pribadi-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading your results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-8">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => router.push("/dashboard")} variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Your Individual Assessment Results</h1>
            <p className="text-muted-foreground mt-2">
              Personal assessment completed on {new Date(results?.updated_at || '').toLocaleDateString('id-ID')}
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={downloadResults} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Download Report
            </Button>
            <Button onClick={() => router.push("/dashboard")} variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>
        </div>
      </div>

      {results && (
        <div className="space-y-6">
          {/* Overall Score */}
          <Card>
            <CardHeader>
              <CardTitle>Overall Assessment Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-4">
                <div className={`text-4xl font-bold ${getScoreColor(results.overall_score)}`}>
                  {results.overall_score}%
                </div>
                <Badge 
                  variant={results.overall_score >= 70 ? "default" : "destructive"}
                  className="text-lg px-4 py-2"
                >
                  {getScoreLevel(results.overall_score)}
                </Badge>
                <Progress value={results.overall_score} className="h-3" />
              </div>
            </CardContent>
          </Card>

          {/* Domain Scores */}
          <Card>
            <CardHeader>
              <CardTitle>Domain Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.domains.map((domain, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-semibold text-lg">{domain.domain}</h3>
                      <span className={`font-bold ${getScoreColor(domain.score)}`}>
                        {domain.score}%
                      </span>
                    </div>
                    <Progress value={domain.score} className="h-2 mb-3" />
                    <Badge variant={domain.score >= 70 ? "default" : "secondary"}>
                      {getScoreLevel(domain.score)}
                    </Badge>
                    
                    {domain.subcategories && (
                      <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                        {Object.entries(domain.subcategories).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-muted-foreground">{key}:</span>
                            <span className="font-medium">{value}%</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {results.domains
                  .filter(domain => domain.score < 70)
                  .map((domain, index) => (
                    <div key={index} className="bg-blue-50 p-3 rounded-lg">
                      <h4 className="font-medium text-blue-900">{domain.domain}</h4>
                      <p className="text-sm text-blue-800 mt-1">
                        Consider focusing on improving this area. Your current score is {domain.score}%.
                        {domain.score < 50 && " This area needs significant attention."}
                      </p>
                    </div>
                  ))}
                {results.domains.every(domain => domain.score >= 70) && (
                  <div className="bg-green-50 p-3 rounded-lg">
                    <p className="text-green-800">
                      Excellent work! All your domain scores are above 70%. Continue maintaining these positive patterns.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
