-- Fix all RLS policies for couple connection functionality
-- Run this in Supabase SQL Editor

-- 1. Fix individual_results policies
DROP POLICY IF EXISTS "Users can view their own results" ON individual_results;
CREATE POLICY "Users can view their own results"
  ON individual_results FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own results" ON individual_results;
CREATE POLICY "Users can insert their own results"
  ON individual_results FOR INSERT
  WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own results" ON individual_results;
CREATE POLICY "Users can update their own results"
  ON individual_results FOR UPDATE
  USING (auth.uid() = user_id);

-- 2. Fix couples policies (more permissive for debugging)
DROP POLICY IF EXISTS "Users can view their couples" ON couples;
CREATE POLICY "Users can view their couples"
  ON couples FOR SELECT
  USING (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

DROP POLICY IF EXISTS "Users can create couples" ON couples;
CREATE POLICY "Users can create couples"
  ON couples FOR INSERT
  WITH CHECK (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

-- 3. Fix profiles policies
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
CREATE POLICY "Users can view their own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

-- Allow users to view partner profiles (for couple connections)
DROP POLICY IF EXISTS "Users can view partner profiles" ON profiles;
CREATE POLICY "Users can view partner profiles"
  ON profiles FOR SELECT
  USING (
    auth.uid() = id 
    OR EXISTS (
      SELECT 1 FROM couples 
      WHERE (user_id_1 = auth.uid() AND user_id_2 = profiles.id)
         OR (user_id_2 = auth.uid() AND user_id_1 = profiles.id)
    )
  );

DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
CREATE POLICY "Users can update their own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
CREATE POLICY "Users can insert their own profile"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- 4. Verify all policies are created
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename IN ('couples', 'individual_results', 'profiles', 'couple_invitation_codes')
ORDER BY tablename, cmd, policyname;
